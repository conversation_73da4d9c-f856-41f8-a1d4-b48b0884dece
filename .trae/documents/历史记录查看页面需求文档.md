## 1. 产品概述

本文档旨在为多智能体对话系统（Genie）设计一个完整的历史记录查看页面，解决用户无法查看过往对话记录和生成结果的问题。该功能将帮助用户更好地管理和回顾与智能体的交互历史，提升用户体验和工作效率。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 默认用户 | 无需注册，直接访问 | 可查看所有历史记录、搜索筛选、访问生成文件 |

### 2.2 功能模块

本历史记录查看页面包含以下核心页面：

1. **历史记录主页**：会话列表展示、搜索筛选功能
2. **会话详情页**：单个会话的完整消息记录查看
3. **文件管理页**：生成文件和结果的集中管理和访问

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 历史记录主页 | 会话列表 | 展示所有历史会话，包含会话标题、时间、状态等信息 |
| 历史记录主页 | 搜索筛选 | 支持按关键词、时间范围、任务类型筛选会话 |
| 历史记录主页 | 快速操作 | 提供删除、收藏、导出等会话管理功能 |
| 会话详情页 | 消息时间线 | 按时间顺序展示完整的对话流程，包括用户输入、智能体响应、任务执行过程 |
| 会话详情页 | 任务展示 | 显示任务拆解、执行状态、工具调用结果等详细信息 |
| 会话详情页 | 结果预览 | 内嵌预览生成的文件、报告、图表等结果 |
| 文件管理页 | 文件列表 | 展示所有生成的文件，支持按类型、大小、创建时间排序 |
| 文件管理页 | 文件预览 | 支持在线预览Markdown、HTML、CSV等文件格式 |
| 文件管理页 | 文件操作 | 提供下载、删除、重命名等文件管理功能 |

## 3. 核心流程

用户访问历史记录功能的主要操作流程如下：

**主要用户流程：**
1. 用户从主页导航进入历史记录页面
2. 浏览会话列表，可使用搜索和筛选功能快速定位目标会话
3. 点击特定会话进入详情页查看完整对话记录
4. 在会话详情中查看任务执行过程和生成的结果
5. 通过文件管理页面统一管理所有生成的文件

```mermaid
graph TD
    A[主页] --> B[历史记录主页]
    B --> C[会话详情页]
    B --> D[文件管理页]
    C --> E[结果预览]
    D --> F[文件下载]
    C --> G[返回会话列表]
    D --> G
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：主色 #4040ff，辅助色 #f5f5f5
- **按钮样式**：圆角按钮，悬停效果，与现有系统保持一致
- **字体**：系统默认字体，标题 16px，正文 14px，辅助信息 12px
- **布局风格**：卡片式布局，左侧导航，响应式设计
- **图标风格**：使用现有的 RelayIcon 字体图标库

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 历史记录主页 | 会话列表 | 卡片式布局，每个会话卡片包含标题、时间戳、状态标签、预览文本。悬停效果，点击进入详情 |
| 历史记录主页 | 搜索筛选 | 顶部搜索框，下拉筛选器（时间范围、任务类型），标签式快速筛选 |
| 历史记录主页 | 分页导航 | 底部分页组件，支持页码跳转和每页条数设置 |
| 会话详情页 | 消息时间线 | 左侧时间轴，右侧消息气泡，区分用户和智能体消息样式 |
| 会话详情页 | 任务面板 | 右侧固定面板，展示任务列表、执行状态、工具调用详情 |
| 会话详情页 | 结果展示 | 内嵌iframe或组件预览，支持全屏查看 |
| 文件管理页 | 文件网格 | 网格布局，文件缩略图、名称、大小、类型图标 |
| 文件管理页 | 操作工具栏 | 顶部工具栏，包含批量操作、视图切换、排序选项 |

### 4.3 响应式设计

- **桌面优先**：主要针对桌面端设计，1200px以上最佳体验
- **移动适配**：768px以下自动调整为单列布局，隐藏次要信息
- **触控优化**：移动端增大点击区域，优化滑动和手势操作

## 5. 技术实现方案

### 5.1 前端技术栈
- **框架**：React 18 + TypeScript
- **UI组件库**：Ant Design 5.x
- **状态管理**：React Hooks + Context API
- **数据持久化**：localStorage + IndexedDB
- **路由管理**：React Router 6

### 5.2 数据存储设计

**前端存储：**
- localStorage：存储用户偏好设置、筛选条件
- IndexedDB：存储会话数据、消息记录（支持离线访问）

**后端扩展：**
- 新增数据库表：sessions（会话表）、messages（消息表）
- 扩展现有fileinfo表：添加session_id关联字段

### 5.3 API接口设计

**新增接口：**
- GET /api/sessions - 获取会话列表
- GET /api/sessions/:id - 获取会话详情
- GET /api/sessions/:id/messages - 获取会话消息
- DELETE /api/sessions/:id - 删除会话
- GET /api/files - 获取文件列表
- GET /api/files/:id/download - 下载文件

### 5.4 性能优化
- **虚拟滚动**：处理大量历史记录的展示
- **懒加载**：按需加载会话详情和文件内容
- **缓存策略**：合理使用浏览器缓存和内存缓存
- **分页加载**：避免一次性加载过多数据

## 6. 数据安全与隐私

- **数据加密**：敏感信息在本地存储时进行加密
- **访问控制**：确保用户只能访问自己的历史记录
- **数据清理**：提供手动和自动清理过期数据的功能
- **导出功能**：支持用户导出自己的历史数据

## 7. 开发优先级

**第一阶段（MVP）：**
1. 历史记录主页 - 基础会话列表展示
2. 会话详情页 - 消息时间线查看
3. 基础搜索功能

**第二阶段：**
1. 文件管理页面
2. 高级筛选功能
3. 数据持久化完善

**第三阶段：**
1. 性能优化
2. 移动端适配
3. 数据导出功能