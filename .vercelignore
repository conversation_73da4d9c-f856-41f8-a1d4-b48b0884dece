# Exclude backend services
genie-backend/
genie-client/
genie-tool/

# Exclude documentation and scripts
docs/
*.sh
*.md
Dockerfile
Deploy.md
LICENSE
NOTICE-Third Party
check_dep_port.sh
start_genie.sh
Genie_start.sh

# Exclude IDE files
.idea/
.vscode/

# Exclude logs and temp files
logs/
*.log
*.db
.first_run_completed

# Keep only ui directory for frontend deployment
# Everything else will be ignored