# MCP客户端Dockerfile (Railway部署)
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install uv

# 复制项目文件
COPY genie-client/ .

# 创建虚拟环境并安装依赖
RUN uv venv .venv && \
    . .venv/bin/activate && \
    uv pip install -r pyproject.toml

# 创建非root用户
RUN addgroup --system python && adduser --system python --ingroup python
RUN chown -R python:python /app
USER python:python

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:1602/health || exit 1

# 暴露端口
EXPOSE 1602

# 启动服务
CMD [".venv/bin/python", "server.py"]