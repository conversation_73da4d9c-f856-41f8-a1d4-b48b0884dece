# 页面快照功能实现方案

## 概述

基于"状态快照 + 渲染器复用"的架构，实现了任务完成时的页面内容保存机制。该方案确保历史回放能够完整还原实时执行时的三个标签页状态，提供与实时页面完全一致的显示效果和交互体验。

## 核心特性

### 1. 智能触发机制
- **自动保存**：任务完成时（`taskStatus` 变为完成状态且 `isFinal` 为 true）自动触发
- **手动保存**：提供"保存记录"按钮，用户可主动触发
- **保存确认**：显示保存成功的提示信息

### 2. 完整状态捕获
- **实时跟踪页面**：最后一个任务的完整执行结果、所有工具调用的输出、任务执行时序
- **浏览器页面**：所有搜索查询记录、访问的网页链接、搜索结果内容
- **文件页面**：所有生成的文件列表、文件预览内容、下载链接

### 3. 双重存储保障
- **后端数据库**：主要存储位置，支持跨设备访问
- **前端localStorage**：备份存储，确保数据安全

### 4. 完美历史回放
- **渲染器复用**：直接使用ActionView组件，确保显示效果一致
- **时间轴控制**：支持快照间切换、自动播放、手动控制
- **状态还原**：完整还原三个标签页的最终状态

## 技术架构

### 前端架构

```
┌─────────────────────────────────────────────────────────────┐
│                    ChatView (主容器)                        │
├─────────────────────────────────────────────────────────────┤
│  usePageSnapshotCapture Hook                               │
│  ├─ 监控任务状态变化                                        │
│  ├─ 自动触发快照保存                                        │
│  └─ 管理保存配置                                           │
├─────────────────────────────────────────────────────────────┤
│  ActionView (工作空间)                                      │
│  ├─ 集成快照保存按钮                                        │
│  ├─ 状态监控Hook                                           │
│  └─ 手动保存功能                                           │
├─────────────────────────────────────────────────────────────┤
│  页面状态捕获工具                                           │
│  ├─ extractFollowPageState()                              │
│  ├─ extractBrowserPageState()                             │
│  ├─ extractFilePageState()                                │
│  └─ captureCompletePageSnapshot()                         │
├─────────────────────────────────────────────────────────────┤
│  快照保存服务                                               │
│  ├─ 后端API调用                                            │
│  ├─ localStorage备份                                       │
│  └─ 错误处理和重试                                         │
└─────────────────────────────────────────────────────────────┘
```

### 后端架构

```
┌─────────────────────────────────────────────────────────────┐
│              PageSnapshotController                         │
│  ├─ POST /snapshots (保存快照)                              │
│  ├─ GET /snapshots/{id} (加载快照)                          │
│  ├─ GET /snapshots/session/{sessionId} (获取会话快照)       │
│  └─ DELETE /snapshots/{id} (删除快照)                       │
├─────────────────────────────────────────────────────────────┤
│              PageSnapshotService                            │
│  ├─ 快照数据验证和处理                                      │
│  ├─ 业务逻辑封装                                           │
│  └─ 统计和分析功能                                         │
├─────────────────────────────────────────────────────────────┤
│              PageSnapshotRepository                         │
│  ├─ 数据库操作封装                                         │
│  ├─ SQL查询优化                                            │
│  └─ 数据一致性保障                                         │
├─────────────────────────────────────────────────────────────┤
│              SQLite Database                                │
│  └─ page_snapshot 表                                       │
│     ├─ 快照基本信息                                        │
│     ├─ JSON格式的完整状态数据                               │
│     └─ 索引优化                                           │
└─────────────────────────────────────────────────────────────┘
```

## 数据结构

### 快照数据结构
```typescript
interface CompletePageSnapshot {
  id: string;                    // 快照ID
  sessionId: string;             // 会话ID
  timestamp: number;             // 快照时间
  type: 'auto' | 'manual';       // 保存类型
  taskStatus: number;            // 任务状态
  isFinal: boolean;              // 是否最终状态
  
  activeView: 'follow' | 'browser' | 'file';  // 当前活跃标签页
  
  // 三个标签页的完整状态
  followPage: FollowPageState;   // 实时跟踪页面状态
  browserPage: BrowserPageState; // 浏览器页面状态
  filePage: FilePageState;       // 文件页面状态
  
  chatInfo: {                    // 聊天基础信息
    query: string;
    response?: string;
    thought?: string;
    plan?: CHAT.Plan;
  };
  
  metadata: {                    // 元数据
    userAgent: string;
    screenResolution: string;
    saveReason: string;
  };
}
```

### 数据库表结构
```sql
CREATE TABLE page_snapshot (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    type TEXT NOT NULL DEFAULT 'auto',
    is_final BOOLEAN NOT NULL DEFAULT 0,
    task_status INTEGER NOT NULL DEFAULT 0,
    active_view TEXT NOT NULL DEFAULT 'follow',
    snapshot_data_json TEXT NOT NULL,
    create_time INTEGER NOT NULL
);
```

## 使用方法

### 1. 基本使用

```typescript
// 在ChatView中集成
const { 
  updateActionViewState,
  saveManualSnapshot,
  shouldAutoSave
} = usePageSnapshotCapture({
  sessionId,
  chatItem: currentChatItem,
  enableAutoSave: true,
  onSaveSuccess: (snapshotId) => {
    message.success(`页面状态已自动保存`);
  }
});

// 在ActionView中添加保存按钮
<ActionView
  sessionId={sessionId}
  chatItem={currentChatItem}
  showSaveButton={shouldAutoSave}
  onSnapshotSaved={(snapshotId) => {
    console.log('快照保存成功:', snapshotId);
  }}
/>
```

### 2. 历史回放

```typescript
// 使用新的历史回放组件
<HistoryReplayV2 
  sessionId="session-123"
  showDialogue={true}
  className="h-full"
/>
```

### 3. 配置管理

```typescript
// 获取和更新配置
const config = pageSnapshotService.getConfig();
pageSnapshotService.updateConfig({
  autoSave: true,
  saveOnTaskComplete: true,
  showSaveConfirmation: true,
  maxSnapshots: 50
});
```

## 部署步骤

### 1. 代码重置
```bash
# 执行重置脚本
chmod +x reset_codebase.sh
./reset_codebase.sh
```

### 2. 前端部署
```bash
# 安装新依赖
cd ui
npm install

# 复制新文件到项目中
# - ui/src/types/pageSnapshot.ts
# - ui/src/utils/pageSnapshotCapture.ts
# - ui/src/services/pageSnapshotService.ts
# - ui/src/hooks/usePageSnapshotCapture.ts
# - ui/src/components/HistoryReplayV2/index.tsx

# 修改现有文件
# - ui/src/components/ActionView/ActionView.tsx
# - ui/src/components/ChatView/index.tsx
```

### 3. 后端部署
```bash
# 复制新文件到项目中
# - genie-backend/src/main/java/com/jd/genie/controller/PageSnapshotController.java
# - genie-backend/src/main/java/com/jd/genie/service/PageSnapshotService.java
# - genie-backend/src/main/java/com/jd/genie/model/PageSnapshotRecord.java
# - genie-backend/src/main/java/com/jd/genie/repo/PageSnapshotRepository.java

# 执行数据库迁移
# - genie-backend/src/main/resources/db/migration/V3__Create_page_snapshot_table.sql
```

### 4. 验证部署
1. 启动后端服务
2. 启动前端服务
3. 创建新的对话任务
4. 等待任务完成，验证自动保存功能
5. 点击"保存记录"按钮，验证手动保存功能
6. 访问历史页面，验证回放功能

## 性能优化

### 1. 存储优化
- 只在任务完成时保存，避免频繁操作
- JSON压缩存储，减少数据库大小
- 定期清理过期快照

### 2. 加载优化
- 快照列表分页加载
- 快照详情按需加载
- 缓存机制减少重复请求

### 3. 内存优化
- 大型快照数据流式处理
- 及时释放不需要的快照数据
- 限制同时加载的快照数量

## 监控和维护

### 1. 数据质量监控
```typescript
// 快照数据验证
const validation = validateSnapshot(snapshot);
if (!validation.isValid) {
  console.error('快照数据验证失败:', validation.errors);
}
```

### 2. 存储空间监控
```typescript
// 获取统计信息
const stats = await pageSnapshotService.getSnapshotStats();
console.log('快照统计:', stats);
```

### 3. 自动清理
```typescript
// 清理过期快照
const cleanedCount = await pageSnapshotService.cleanupExpiredSnapshots(
  30 * 24 * 60 * 60 * 1000 // 30天
);
```

## 总结

该实现方案通过"状态快照 + 渲染器复用"的架构，完美解决了历史回放与实时页面显示不一致的问题。主要优势：

✅ **完整性**：保存任务完成时的完整页面状态
✅ **一致性**：历史回放与实时页面显示完全一致
✅ **可靠性**：双重存储保障，数据安全可靠
✅ **易用性**：自动保存 + 手动保存，操作简单
✅ **性能**：只在关键时刻保存，性能影响最小
✅ **扩展性**：模块化设计，易于扩展和维护

通过这个方案，用户可以完整回顾任务执行过程，获得与实时执行时完全一致的体验。
