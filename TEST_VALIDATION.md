# 历史数据持久化和回放功能测试验证指南

## 测试环境准备

### 1. 后端服务启动
确保以下服务正常运行：
- Spring Boot 应用
- SQLite 数据库
- 历史数据相关的所有服务和控制器

### 2. 前端应用启动
确保前端应用正常运行并能访问后端API

## 测试步骤

### 第一阶段：数据保存功能测试

#### 1.1 创建新的任务执行会话
1. 通过前端界面发起一个新的任务请求
2. 观察任务执行过程中的各种输出类型：
   - thinks（思考过程）
   - tasks（任务执行）
   - deep search（深度搜索）
   - files（文件操作）
   - reports（报告生成）

#### 1.2 验证数据库保存
执行以下SQL查询验证数据是否正确保存：

```sql
-- 检查会话数据
SELECT * FROM session WHERE session_id = 'your_session_id';

-- 检查消息数据
SELECT * FROM message WHERE session_id = 'your_session_id' ORDER BY timestamp;

-- 检查事件数据
SELECT * FROM event WHERE session_id = 'your_session_id' ORDER BY create_time;

-- 检查文件数据
SELECT * FROM file_ref WHERE session_id = 'your_session_id' ORDER BY create_time;
```

#### 1.3 验证事件数据完整性
检查事件表中的关键字段：
- `task_id` 不应为空（对于task相关事件）
- `message_order` 应有合理的顺序值
- `task_order` 应有合理的顺序值
- `payload_json` 应包含完整的事件数据

### 第二阶段：历史回放功能测试

#### 2.1 访问历史记录页面
1. 导航到历史记录列表页面
2. 找到刚才创建的会话
3. 点击查看详情

#### 2.2 验证回放显示
1. 检查回放视图是否正确显示：
   - 用户查询
   - 思考过程
   - 任务执行步骤
   - 工具调用结果
   - 文件生成
   - 最终结果

2. 对比实时执行和历史回放的显示：
   - 布局是否一致
   - 内容是否完整
   - 交互功能是否正常

#### 2.3 验证数据完整性
1. 检查所有事件是否按正确顺序显示
2. 验证文件链接是否可用
3. 确认所有类型的输出都能正确渲染

### 第三阶段：数据验证工具测试

#### 3.1 使用后端验证API
```bash
# 验证单个会话
curl -X GET "http://localhost:8080/web/api/v1/history/sessions/{sessionId}/validate"

# 批量验证会话
curl -X POST "http://localhost:8080/web/api/v1/history/validate/batch" \
  -H "Content-Type: application/json" \
  -d '{"sessionIds": ["session1", "session2"]}'
```

#### 3.2 使用前端验证页面
1. 访问 `/history-validation` 页面
2. 输入要验证的会话ID
3. 点击"开始验证"
4. 查看验证结果和统计信息

### 第四阶段：边界情况测试

#### 4.1 异常数据处理
1. 测试包含特殊字符的数据
2. 测试超长内容的处理
3. 测试网络中断时的数据保存

#### 4.2 性能测试
1. 测试大量历史数据的加载性能
2. 测试复杂任务的回放性能
3. 测试并发访问的稳定性

## 验证检查清单

### 数据保存验证 ✓
- [ ] 会话基本信息正确保存
- [ ] 用户消息完整保存
- [ ] 助手回复完整保存
- [ ] 所有事件按时间顺序保存
- [ ] 事件包含完整的载荷数据
- [ ] 任务ID和顺序信息正确
- [ ] 文件引用信息完整

### 历史回放验证 ✓
- [ ] 回放视图正确重建
- [ ] 显示格式与实时一致
- [ ] 所有类型输出正确渲染
- [ ] 交互功能正常工作
- [ ] 文件预览功能正常
- [ ] 时间序列正确

### 数据完整性验证 ✓
- [ ] 无数据丢失
- [ ] 无格式错误
- [ ] 无时间序列错误
- [ ] 无关联关系错误

### 性能验证 ✓
- [ ] 数据保存性能良好
- [ ] 历史加载性能良好
- [ ] 回放渲染性能良好
- [ ] 内存使用合理

## 问题排查指南

### 常见问题及解决方案

#### 1. 事件数据缺失taskId
**症状**：历史回放时任务顺序混乱
**检查**：查看event表中task_id字段是否为空
**解决**：确认MultiAgentServiceImpl中的extractTaskId方法正常工作

#### 2. 回放显示格式不一致
**症状**：历史回放与实时显示差异较大
**检查**：查看buildReplayChat函数的数据重构逻辑
**解决**：确认事件载荷数据结构正确

#### 3. 文件链接失效
**症状**：历史回放中文件无法访问
**检查**：查看file_ref表中的URL是否正确
**解决**：确认文件存储和URL生成逻辑

#### 4. 性能问题
**症状**：历史数据加载缓慢
**检查**：查看数据库查询性能和索引
**解决**：优化查询语句，添加必要索引

## 测试报告模板

### 测试环境
- 后端版本：
- 前端版本：
- 数据库版本：
- 测试时间：

### 测试结果
- 数据保存功能：✓/✗
- 历史回放功能：✓/✗
- 数据验证工具：✓/✗
- 性能表现：✓/✗

### 发现的问题
1. 问题描述
   - 重现步骤
   - 预期结果
   - 实际结果
   - 严重程度

### 改进建议
1. 功能改进
2. 性能优化
3. 用户体验

## 持续监控

### 生产环境监控指标
1. 数据保存成功率
2. 历史回放访问频率
3. 数据验证错误率
4. 系统性能指标

### 定期维护任务
1. 数据完整性检查
2. 性能优化评估
3. 存储空间清理
4. 功能更新测试
