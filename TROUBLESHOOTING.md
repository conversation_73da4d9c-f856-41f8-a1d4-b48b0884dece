# 页面快照功能故障排除指南

## 当前问题诊断

### 问题描述
在运行过程中遇到了以下错误：
- 后端编译失败：`ApiResponse cannot be resolved to a type`
- 工具服务404错误：`GET / HTTP/1.1" 404 Not Found`
- 进程被中断：`^C`

### 问题分析

#### 1. 后端编译问题
**原因**: 使用了不存在的 `ApiResponse` 类
**解决方案**: ✅ 已修复 - 将所有 `ApiResponse` 替换为 `ResponseEntity`

#### 2. 工具服务404问题
**原因**: 工具服务的根路径 `/` 没有对应的处理器
**影响**: 不影响页面快照功能，这是正常现象

#### 3. 进程中断问题
**原因**: 用户手动中断了进程
**解决方案**: 使用提供的启动脚本重新启动服务

## 解决方案

### 方案1: 快速修复（推荐）

由于编译错误已经修复，现在可以直接启动服务：

```bash
# 1. 给脚本执行权限
chmod +x start_with_snapshot.sh
chmod +x test_page_snapshot.sh

# 2. 启动完整系统
./start_with_snapshot.sh
```

### 方案2: 手动启动各个服务

如果自动启动脚本有问题，可以手动启动：

```bash
# 1. 启动后端服务
cd genie-backend
./start.sh &

# 2. 启动工具服务
cd ../genie-tool
source .venv/bin/activate  # 如果有虚拟环境
python3 -m genie_tool.main &

# 3. 启动前端服务
cd ../ui
npm start &
```

### 方案3: 使用Docker（如果可用）

```bash
# 如果项目支持Docker
docker-compose up -d
```

## 验证步骤

### 1. 检查服务状态

```bash
# 检查后端服务
curl http://localhost:8080/health

# 检查前端服务
curl http://localhost:3000

# 检查工具服务
curl http://localhost:1601/health
```

### 2. 测试页面快照功能

```bash
# 运行自动化测试
./test_page_snapshot.sh
```

### 3. 手动测试

1. 访问 http://localhost:3000
2. 创建新的对话任务
3. 等待任务完成
4. 观察是否自动保存快照
5. 点击"保存记录"按钮测试手动保存
6. 访问历史页面验证回放功能

## 常见问题解决

### Q1: 后端服务启动失败
**可能原因**:
- 端口8080被占用
- Java版本不兼容
- 数据库连接问题

**解决方案**:
```bash
# 检查端口占用
lsof -i :8080

# 杀死占用进程
kill -9 $(lsof -ti:8080)

# 检查Java版本
java -version  # 需要JDK 17

# 查看后端日志
tail -f genie-backend/logs/application.log
```

### Q2: 前端服务启动失败
**可能原因**:
- 端口3000被占用
- Node.js版本不兼容
- 依赖安装失败

**解决方案**:
```bash
# 检查端口占用
lsof -i :3000

# 重新安装依赖
cd ui
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node -v  # 建议使用 v16+
```

### Q3: 工具服务启动失败
**可能原因**:
- Python环境问题
- 依赖包缺失
- 端口1601被占用

**解决方案**:
```bash
# 检查Python环境
python3 --version

# 重新安装依赖
cd genie-tool
pip install -r requirements.txt

# 或使用uv
uv sync
```

### Q4: 页面快照功能不工作
**可能原因**:
- 数据库表未创建
- API路径错误
- 前端组件未正确集成

**解决方案**:
```bash
# 检查数据库表
sqlite3 genie-backend/genie-history.db ".tables"

# 手动创建表
sqlite3 genie-backend/genie-history.db < genie-backend/src/main/resources/db/migration/V3__Create_page_snapshot_table.sql

# 测试API
curl -X POST http://localhost:8080/web/api/v1/snapshots \
  -H "Content-Type: application/json" \
  -d '{"snapshot":{"id":"test","sessionId":"test","timestamp":1234567890}}'
```

## 性能优化建议

### 1. 数据库优化
- 定期清理过期快照
- 优化查询索引
- 考虑数据压缩

### 2. 前端优化
- 启用快照缓存
- 优化大型快照加载
- 实现懒加载

### 3. 后端优化
- 异步处理快照保存
- 实现批量操作
- 添加监控和日志

## 监控和维护

### 1. 日志监控
```bash
# 后端日志
tail -f genie-backend/logs/application.log

# 前端日志
# 查看浏览器控制台

# 工具服务日志
tail -f genie-tool/logs/genie_tool.log
```

### 2. 性能监控
```bash
# 检查内存使用
ps aux | grep -E "(java|node|python)"

# 检查磁盘使用
du -sh genie-backend/genie-history.db

# 检查网络连接
netstat -tulpn | grep -E "(3000|8080|1601)"
```

### 3. 定期维护
```bash
# 清理过期快照
curl -X POST http://localhost:8080/web/api/v1/snapshots/cleanup \
  -H "Content-Type: application/json" \
  -d '{"maxAge": 2592000000}'  # 30天

# 备份数据库
cp genie-backend/genie-history.db genie-backend/genie-history.db.backup
```

## 联系支持

如果以上解决方案都无法解决问题，请：

1. 收集错误日志
2. 记录重现步骤
3. 提供系统环境信息
4. 查看项目文档或提交Issue

## 快速恢复检查清单

- [ ] 所有服务正常启动（3000, 8080, 1601端口）
- [ ] 页面快照API测试通过
- [ ] 前端页面正常访问
- [ ] 自动保存功能正常
- [ ] 手动保存功能正常
- [ ] 历史回放功能正常
- [ ] 数据库表结构正确
- [ ] 日志无严重错误
