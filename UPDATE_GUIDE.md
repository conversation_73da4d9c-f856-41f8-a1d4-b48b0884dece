# JoyAgent-JDGenie 项目更新指南

## 📊 当前状态分析

### 🔍 检查结果
- **远程更新**: ✅ 发现 12 个新提交需要拉取
- **本地修改**: ⚠️ 检测到重要配置文件有本地修改
- **冲突风险**: ✅ 远程更新主要涉及文档文件，配置文件无冲突

### 📁 远程更新涉及的文件
```
Genie_start.sh      # 启动脚本
LICENSE            # 许可证文件  
README.md          # 项目文档
contributor_EN.pdf # 贡献者协议(英文)
contributor_ZH.pdf # 贡献者协议(中文)
```

### 📝 本地修改的文件
```
genie-backend/pom.xml                           # Maven配置
genie-backend/src/main/resources/application.yml # 后端配置 ⚠️
genie-tool/.env_template                        # 环境变量模板
ui/pnpm-lock.yaml                              # 前端依赖锁定
```

## 🚀 推荐更新方案

### 方案一：自动化安全更新 (推荐)

使用我们创建的自动化脚本：

```bash
# 执行安全更新脚本
./update_project.sh
```

**脚本功能：**
- ✅ 自动备份重要配置文件
- ✅ 检查远程更新和潜在冲突
- ✅ 安全拉取远程更新
- ✅ 保留本地配置文件修改
- ✅ 提供详细的更新报告

### 方案二：手动分步更新

如果你想更精细地控制更新过程：

#### 1. 备份配置文件
```bash
# 创建备份目录
mkdir -p config_backup_$(date +%Y%m%d_%H%M%S)

# 备份重要配置文件
cp genie-backend/src/main/resources/application.yml config_backup_*/
cp genie-tool/.env config_backup_*/
cp genie-tool/.env_template config_backup_*/
```

#### 2. 暂存本地修改
```bash
# 暂存所有本地修改
git stash push -m "Backup local changes before update"
```

#### 3. 拉取远程更新
```bash
# 拉取最新代码
git pull origin main
```

#### 4. 恢复配置文件
```bash
# 从备份恢复配置文件
cp config_backup_*/application.yml genie-backend/src/main/resources/
cp config_backup_*/.env genie-tool/
cp config_backup_*/.env_template genie-tool/
```

## 🔧 配置文件保护策略

### 重要配置文件列表
1. **`genie-backend/src/main/resources/application.yml`**
   - 包含API密钥和数据库配置
   - 包含LLM模型配置
   - ⚠️ 绝对不能丢失

2. **`genie-tool/.env`**
   - 包含OpenAI API密钥
   - 包含搜索引擎API密钥
   - 包含文件路径配置
   - ⚠️ 绝对不能丢失

3. **`genie-tool/.env_template`**
   - 环境变量模板文件
   - 可能有本地定制

### 配置文件版本管理建议

#### 创建本地配置分支
```bash
# 创建专门的本地配置分支
git checkout -b local-config
git add genie-backend/src/main/resources/application.yml
git add genie-tool/.env
git commit -m "Local configuration files"

# 回到主分支
git checkout main
```

#### 使用 .gitignore 保护敏感配置
在项目根目录的 `.gitignore` 中添加：
```
# 本地配置文件
genie-tool/.env
genie-backend/src/main/resources/application-local.yml
```

## 📋 更新后检查清单

### ✅ 必须检查的项目

1. **配置文件完整性**
   ```bash
   # 检查配置文件是否存在
   ls -la genie-backend/src/main/resources/application.yml
   ls -la genie-tool/.env
   ```

2. **API密钥有效性**
   - 检查 `application.yml` 中的 OpenAI API Key
   - 检查 `.env` 中的各种API密钥
   - 确认密钥格式正确

3. **服务启动测试**
   ```bash
   # 测试后端服务
   cd genie-backend && ./start.sh
   
   # 测试工具服务
   cd genie-tool && ./start.sh
   
   # 测试客户端服务
   cd genie-client && ./start.sh
   ```

4. **功能验证**
   - 访问前端界面: http://localhost:3000
   - 测试API接口: http://localhost:8080
   - 验证工具服务: http://localhost:1601

## 🆘 故障恢复方案

### 如果更新后出现问题

#### 1. 快速回滚
```bash
# 回滚到更新前的状态
git reset --hard HEAD~12  # 回滚12个提交

# 从备份恢复配置文件
cp config_backup_*/* ./对应目录/
```

#### 2. 重新部署
```bash
# 使用部署脚本重新部署
./deploy.sh
```

#### 3. 检查日志
```bash
# 查看各服务日志
tail -f genie-backend/logs/*.log
tail -f genie-tool/logs/*.log
tail -f genie-client/logs/*.log
```

## 🔄 定期更新建议

### 更新频率
- **每周检查**: 使用 `git fetch origin` 检查更新
- **每月更新**: 执行完整的项目更新
- **重要更新**: 关注项目Release页面

### 自动化监控
创建定期检查脚本：
```bash
#!/bin/bash
# check_updates.sh
cd /Users/<USER>/git_project_vscode/04-mutil-agents/joyagent-jdgenie
git fetch origin
COMMITS_BEHIND=$(git rev-list --count HEAD..origin/main)
if [ "$COMMITS_BEHIND" -gt 0 ]; then
    echo "🔔 发现 $COMMITS_BEHIND 个新提交，建议更新项目"
    git log --oneline HEAD..origin/main
fi
```

## 📞 技术支持

如果在更新过程中遇到问题：

1. **检查备份文件**: 确保配置文件备份完整
2. **查看Git状态**: `git status` 和 `git log`
3. **恢复到安全状态**: 使用备份文件恢复
4. **重新执行更新**: 使用自动化脚本重试

---

**⚠️ 重要提醒**: 
- 更新前务必备份重要配置文件
- 确保API密钥等敏感信息不会丢失
- 更新后及时验证所有服务功能正常