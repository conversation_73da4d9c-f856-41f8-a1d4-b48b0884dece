
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_BASE_URL=https://api.openai.com/v1

# <a href="https://docs.litellm.ai/docs/providers">其他模型支持文档</a>
# ANTHROPIC_API_KEY=<or anthropic api key>
# ANTHROPIC_API_BASE=<your base url>

# 敏感词过滤
SENSITIVE_WORD_REPLACE=true

# 文件系统路径配置
FILE_SAVE_PATH=/Users/<USER>/Desktop/15_temp 

SQLITE_DB_PATH=autobots.db
FILE_SERVER_URL=http://127.0.0.1:1601/v1/file_tool

# DeepSearch 配置
USE_JD_SEARCH_GATEWAY=false
USE_SEARCH_ENGINE=serp
SEARCH_COUNT=10
SEARCH_TIMEOUT=10
SEARCH_THREAD_NUM=5

DEFAULT_MODEL=gpt-4.1

QUERY_DECOMPOSE_MODEL=${DEFAULT_MODEL}
QUERY_DECOMPOSE_THINK_MODEL=${DEFAULT_MODEL}
QUERY_DECOMPOSE_MAX_SIZE=5
SEARCH_REASONING_MODEL=${DEFAULT_MODEL}
SEARCH_ANSWER_MODEL=${DEFAULT_MODEL}
SEARCH_ANSWER_LENGTH=10000
REPORT_MODEL=${DEFAULT_MODEL}

SINGLE_PAGE_MAX_SIZE=0

BING_SEARCH_URL=
BING_SEARCH_API_KEY=

JINA_SEARCH_URL=https://s.jina.ai/
JINA_SEARCH_API_KEY=jina_5718fe385d9e4515b2eefde7016a44beg6ZFzO8HrP2IY6jfo6zi9wX4w1bR

SOGOU_SEARCH_URL=
SOGOU_SEARCH_API_KEY=

SERPER_SEARCH_URL=https://google.serper.dev/search
SERPER_SEARCH_API_KEY=8142ffdbb49b2cec4ddecb9f759a0196a375521a

# Code Interpreter 配置
CODE_INTEPRETER_MODEL=${DEFAULT_MODEL}
