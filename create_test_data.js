// 创建测试数据的脚本
const API_BASE = 'http://localhost:8080';

// 创建一个包含完整数据的测试会话
async function createTestSession() {
    const sessionId = `test-session-${Date.now()}`;
    const messageId1 = `${sessionId}-msg-1`;
    const messageId2 = `${sessionId}-msg-2`;
    const fileId = `${sessionId}-file-1`;
    
    // 准备批量导入数据
    const importData = {
        sessions: [{
            sessionId: sessionId,
            title: '测试会话 - 包含完整过程回放数据',
            status: 'completed',
            summary: '这是一个包含完整思考过程和工具结果的测试会话',
            productType: 'genie',
            tags: ['测试', 'React', '组件开发'],
            createTime: Date.now(),
            updateTime: Date.now()
        }],
        messages: [
            {
                id: messageId1,
                sessionId: sessionId,
                type: 'user',
                content: '请帮我创建一个React组件',
                timestamp: Date.now(),
                thought: null,
                planThought: null,
                toolThought: null,
                taskStatus: 0,
                resultMap: null
            },
            {
                id: messageId2,
                sessionId: sessionId,
                type: 'assistant',
                content: '我将为您创建一个React组件。让我先分析需求，然后编写代码。',
                timestamp: Date.now() + 1000,
                thought: '用户需要一个React组件。我需要：\n1. 分析具体需求\n2. 设计组件结构\n3. 编写代码\n4. 确保代码质量',
                planThought: '计划步骤：\n1. 创建基础组件结构\n2. 添加必要的props\n3. 实现组件逻辑\n4. 添加样式\n5. 测试组件功能',
                toolThought: '我将使用以下工具：\n- create_file: 创建组件文件\n- update_file: 更新代码\n- run_command: 测试组件',
                taskStatus: 1,
                resultMap: {
                    agentType: 1,
                    taskSummary: '已成功创建React组件，包含完整的功能实现和样式设计。组件支持自定义props，具有良好的可复用性。',
                    toolResults: [
                        {
                            toolName: 'create_file',
                            result: '成功创建文件 /src/components/TestComponent.tsx',
                            status: 'success'
                        },
                        {
                            toolName: 'update_file', 
                            result: '成功更新组件样式和功能',
                            status: 'success'
                        }
                    ],
                    searchResults: [
                        {
                            query: 'React组件最佳实践',
                            results: '找到相关的React组件设计模式和最佳实践'
                        }
                    ]
                }
            }
        ],
        files: [{
            id: fileId,
            sessionId: sessionId,
            fileName: 'TestComponent.tsx',
            downloadUrl: '/api/files/download/TestComponent.tsx',
            previewUrl: '/api/files/preview/TestComponent.tsx',
            fileSize: 2048,
            fileType: 'tsx',
            createTime: Date.now()
        }],
        events: []
    };
    
    try {
        console.log('批量导入测试数据...');
        const response = await fetch(`${API_BASE}/web/api/v1/history/import`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(importData)
        });
        
        const result = await response.json();
        console.log('导入结果:', result);
        
        if (result.code === 200) {
            console.log('测试数据创建成功！会话ID:', sessionId);
            return sessionId;
        } else {
            throw new Error(`导入失败: ${result.message}`);
        }
    } catch (error) {
        console.error('创建测试数据失败:', error);
        throw error;
    }
}

// 验证数据
async function verifyTestData(sessionId) {
    try {
        const response = await fetch(`${API_BASE}/web/api/v1/history/sessions/${sessionId}`);
        const data = await response.json();
        console.log('验证数据:', data);
        return data;
    } catch (error) {
        console.error('验证数据失败:', error);
        throw error;
    }
}

// 执行创建和验证
createTestSession()
    .then(sessionId => verifyTestData(sessionId))
    .then(data => {
        console.log('测试数据创建并验证成功!');
        console.log('会话数据:', data.session);
        console.log('消息数量:', data.messages?.length || 0);
        console.log('文件数量:', data.files?.length || 0);
    })
    .catch(error => {
        console.error('操作失败:', error);
    });