<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.danger {
            background: #dc3545;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>🔍 历史记录调试工具</h1>
    
    <div class="container">
        <h2>📊 数据检查</h2>
        <button class="button" onclick="checkLocalStorageData()">检查 localStorage 数据</button>
        <button class="button" onclick="checkBackendData()">检查后端数据</button>
        <button class="button warning" onclick="clearAllData()">清空所有数据</button>
        <div id="checkOutput" class="output" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h2>🛠️ 数据修复</h2>
        <button class="button success" onclick="createTestData()">创建测试数据</button>
        <button class="button" onclick="fixSessionData()">修复会话数据</button>
        <button class="button" onclick="verifyData()">验证数据完整性</button>
        <div id="fixOutput" class="output" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h2>🔗 API测试</h2>
        <button class="button" onclick="testBackendAPI()">测试后端API</button>
        <button class="button" onclick="testSessionDetail()">测试会话详情API</button>
        <div id="apiOutput" class="output" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h2>📝 状态信息</h2>
        <div id="statusInfo"></div>
    </div>

    <script>
        const TARGET_SESSION_ID = 'session-1754633713790-8120';
        
        function showOutput(elementId, content) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = content;
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusInfo');
            const statusElement = document.createElement('div');
            statusElement.className = `status ${type}`;
            statusElement.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            statusDiv.appendChild(statusElement);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        function checkLocalStorageData() {
            showStatus('检查 localStorage 数据...', 'info');
            
            const sessions = localStorage.getItem('genie_history_sessions');
            const messages = localStorage.getItem('genie_history_messages');
            const files = localStorage.getItem('genie_history_files');
            
            let output = '=== localStorage 数据检查 ===\n';
            output += `Sessions: ${sessions ? 'EXISTS' : 'NOT FOUND'}\n`;
            output += `Messages: ${messages ? 'EXISTS' : 'NOT FOUND'}\n`;
            output += `Files: ${files ? 'EXISTS' : 'NOT FOUND'}\n\n`;
            
            if (sessions) {
                try {
                    const sessionsData = JSON.parse(sessions);
                    output += `Sessions count: ${Array.isArray(sessionsData) ? sessionsData.length : 'Invalid format'}\n`;
                    if (Array.isArray(sessionsData) && sessionsData.length > 0) {
                        output += 'Session IDs:\n';
                        sessionsData.forEach(s => {
                            output += `  - ${s.sessionId}: ${s.title || 'No title'}\n`;
                        });
                    }
                } catch (e) {
                    output += `Sessions parse error: ${e.message}\n`;
                }
            }
            
            if (messages) {
                try {
                    const messagesData = JSON.parse(messages);
                    output += `\nMessages format: ${Array.isArray(messagesData) ? 'Array' : 'Object'}\n`;
                    if (typeof messagesData === 'object') {
                        const sessionIds = Object.keys(messagesData);
                        output += `Message sessions: ${sessionIds.length}\n`;
                        sessionIds.forEach(id => {
                            const msgs = messagesData[id];
                            output += `  - ${id}: ${Array.isArray(msgs) ? msgs.length : 'Invalid'} messages\n`;
                        });
                    }
                } catch (e) {
                    output += `Messages parse error: ${e.message}\n`;
                }
            }
            
            showOutput('checkOutput', output);
            showStatus('localStorage 数据检查完成', 'success');
        }
        
        async function checkBackendData() {
            showStatus('检查后端数据...', 'info');
            
            try {
                // 检查后端会话列表
                const sessionsResponse = await fetch('/web/api/v1/history/sessions');
                const sessionsResult = await sessionsResponse.json();
                
                let output = '=== 后端数据检查 ===\n';
                output += `Sessions API Status: ${sessionsResponse.status}\n`;
                output += `Sessions Response: ${JSON.stringify(sessionsResult, null, 2)}\n\n`;
                
                // 如果有会话，检查第一个会话的详情
                if (sessionsResult.data && sessionsResult.data.length > 0) {
                    const firstSessionId = sessionsResult.data[0].sessionId;
                    const detailResponse = await fetch(`/web/api/v1/history/sessions/${firstSessionId}`);
                    const detailResult = await detailResponse.json();
                    
                    output += `Session Detail API Status: ${detailResponse.status}\n`;
                    output += `Session Detail Response: ${JSON.stringify(detailResult, null, 2)}\n`;
                }
                
                showOutput('apiOutput', output);
                showStatus('后端数据检查完成', 'success');
            } catch (error) {
                showOutput('apiOutput', `后端API检查失败: ${error.message}`);
                showStatus('后端数据检查失败', 'error');
            }
        }
        
        function createTestData() {
            showStatus('创建测试数据...', 'info');
            
            // 创建会话数据
            const sessionData = {
                sessionId: TARGET_SESSION_ID,
                title: '帮我构建一个PPT详细介绍 美国放射协会的 ACR-AC 临床检查适应性指南的框架、构建、方法和案例...',
                summary: 'PPT构建任务，关于美国放射协会ACR-AC临床检查适应性指南',
                status: 'completed',
                createTime: 1754633713790,
                updateTime: Date.now(),
                messageCount: 2,
                tags: ['PPT', '医学指南', 'ACR-AC']
            };
            
            // 保存会话
            const existingSessions = JSON.parse(localStorage.getItem('genie_history_sessions') || '[]');
            const sessionIndex = existingSessions.findIndex(s => s.sessionId === TARGET_SESSION_ID);
            if (sessionIndex >= 0) {
                existingSessions[sessionIndex] = sessionData;
            } else {
                existingSessions.push(sessionData);
            }
            localStorage.setItem('genie_history_sessions', JSON.stringify(existingSessions));
            
            // 创建消息数据
            const messageData = [
                {
                    id: 'msg-user-1754633713790',
                    sessionId: TARGET_SESSION_ID,
                    role: 'user',
                    type: 'user',
                    content: '帮我构建一个PPT详细介绍 美国放射协会的 ACR-AC 临床检查适应性指南的框架、构建、方法和案例，当前的进展和应用情况，这个指南的优点和缺点',
                    timestamp: 1754633713790,
                    files: []
                },
                {
                    id: 'msg-assistant-1754633713791',
                    sessionId: TARGET_SESSION_ID,
                    role: 'assistant',
                    type: 'assistant',
                    content: '我将为您创建一个详细的PPT，介绍美国放射协会(ACR)的ACR-AC临床检查适应性指南。这个指南是放射学领域的重要参考文献，我会从框架、构建方法、案例分析等多个角度进行全面介绍。',
                    timestamp: 1754633713791,
                    thought: '用户需要一个关于ACR-AC临床检查适应性指南的详细PPT。我需要从多个维度来构建这个演示文稿，包括指南的背景、框架结构、构建方法论、实际案例、当前进展以及优缺点分析。',
                    toolThought: '我应该使用PPT创建工具来生成一个结构化的演示文稿，确保内容全面且专业。',
                    planThought: '制定PPT创建计划：1. 分析ACR-AC指南的核心内容 2. 设计PPT结构框架 3. 收集相关案例和数据 4. 创建专业的演示文稿',
                    toolResult: {
                        toolName: 'ppt_generator',
                        toolParam: {
                            topic: 'ACR-AC临床检查适应性指南',
                            sections: ['框架介绍', '构建方法', '案例分析', '进展现状', '优缺点评估'],
                            style: 'professional_medical'
                        },
                        toolResult: 'PPT创建成功。包含15张幻灯片，涵盖ACR-AC指南的完整介绍，包括框架结构、方法论、实际应用案例以及当前发展状况的全面分析。'
                    },
                    resultMap: {
                        searchResult: {
                            query: ['ACR-AC临床检查适应性指南', '美国放射协会指南', '临床决策支持系统'],
                            docs: [[
                                {
                                    title: 'ACR Appropriateness Criteria® Overview',
                                    link: 'https://www.acr.org/Clinical-Resources/ACR-Appropriateness-Criteria',
                                    content: 'ACR适应性标准是基于证据的指南，旨在帮助临床医生为特定临床条件选择最合适的影像检查。这些标准由多学科专家小组制定，定期更新以反映最新的医学证据。'
                                },
                                {
                                    title: 'Clinical Decision Support and ACR-AC',
                                    link: 'https://www.acr.org/Practice-Management-and-Economics/Practice-Toolkit/Clinical-Decision-Support',
                                    content: 'ACR-AC指南作为临床决策支持工具，通过标准化的评分系统帮助医生选择最适当的影像检查，减少不必要的辐射暴露，提高诊断效率。'
                                }
                            ]]
                        }
                    },
                    plan: {
                        steps: [
                            { id: 1, title: 'ACR-AC指南背景研究', status: 'completed', description: '收集和分析ACR-AC指南的历史背景和发展历程' },
                            { id: 2, title: 'PPT结构设计', status: 'completed', description: '设计演示文稿的整体结构和内容框架' },
                            { id: 3, title: '内容创建和编辑', status: 'completed', description: '创建详细的幻灯片内容，包括文字、图表和案例' },
                            { id: 4, title: '质量审核和优化', status: 'completed', description: '审核PPT内容的准确性和专业性' }
                        ]
                    },
                    files: []
                }
            ];
            
            // 保存消息（使用对象格式）
            const existingMessages = JSON.parse(localStorage.getItem('genie_history_messages') || '{}');
            existingMessages[TARGET_SESSION_ID] = messageData;
            localStorage.setItem('genie_history_messages', JSON.stringify(existingMessages));
            
            // 创建文件数据
            const fileData = [
                {
                    id: 'file-1754633713792',
                    sessionId: TARGET_SESSION_ID,
                    messageId: 'msg-assistant-1754633713791',
                    fileName: 'ACR-AC临床检查适应性指南.pptx',
                    filePath: '/generated/presentations/acr-ac-guide.pptx',
                    fileType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    fileSize: 2048576,
                    createTime: 1754633713792,
                    downloadUrl: '/api/files/download/acr-ac-guide.pptx'
                }
            ];
            
            // 保存文件（使用对象格式）
            const existingFiles = JSON.parse(localStorage.getItem('genie_history_files') || '{}');
            existingFiles[TARGET_SESSION_ID] = fileData;
            localStorage.setItem('genie_history_files', JSON.stringify(existingFiles));
            
            showStatus('测试数据创建完成', 'success');
            
            let output = '=== 测试数据创建完成 ===\n';
            output += `Session ID: ${TARGET_SESSION_ID}\n`;
            output += `Messages: ${messageData.length}\n`;
            output += `Files: ${fileData.length}\n`;
            output += `\n数据已保存到 localStorage`;
            
            showOutput('fixOutput', output);
        }
        
        function verifyData() {
            showStatus('验证数据完整性...', 'info');
            
            const sessions = JSON.parse(localStorage.getItem('genie_history_sessions') || '[]');
            const messages = JSON.parse(localStorage.getItem('genie_history_messages') || '{}');
            const files = JSON.parse(localStorage.getItem('genie_history_files') || '{}');
            
            const targetSession = sessions.find(s => s.sessionId === TARGET_SESSION_ID);
            const targetMessages = messages[TARGET_SESSION_ID];
            const targetFiles = files[TARGET_SESSION_ID];
            
            let output = '=== 数据完整性验证 ===\n';
            output += `Session 存在: ${!!targetSession}\n`;
            output += `Messages 存在: ${!!targetMessages}\n`;
            output += `Messages 数量: ${targetMessages?.length || 0}\n`;
            output += `Files 存在: ${!!targetFiles}\n`;
            output += `Files 数量: ${targetFiles?.length || 0}\n\n`;
            
            if (targetMessages) {
                const assistantMessages = targetMessages.filter(msg => msg.role === 'assistant');
                output += `助手消息数量: ${assistantMessages.length}\n`;
                
                if (assistantMessages.length > 0) {
                    const msg = assistantMessages[0];
                    output += '过程数据检查:\n';
                    output += `  - thought: ${!!msg.thought}\n`;
                    output += `  - toolThought: ${!!msg.toolThought}\n`;
                    output += `  - planThought: ${!!msg.planThought}\n`;
                    output += `  - toolResult: ${!!msg.toolResult}\n`;
                    output += `  - searchResult: ${!!msg.resultMap?.searchResult}\n`;
                    output += `  - plan: ${!!msg.plan}\n`;
                }
            }
            
            showOutput('fixOutput', output);
            showStatus('数据完整性验证完成', 'success');
        }
        
        async function testBackendAPI() {
            showStatus('测试后端API...', 'info');
            
            try {
                const response = await fetch('/web/api/v1/history/sessions');
                const result = await response.json();
                
                let output = '=== 后端API测试 ===\n';
                output += `Status: ${response.status}\n`;
                output += `Response: ${JSON.stringify(result, null, 2)}`;
                
                showOutput('apiOutput', output);
                showStatus('后端API测试完成', response.ok ? 'success' : 'error');
            } catch (error) {
                showOutput('apiOutput', `API测试失败: ${error.message}`);
                showStatus('后端API测试失败', 'error');
            }
        }
        
        async function testSessionDetail() {
            showStatus('测试会话详情API...', 'info');
            
            try {
                const response = await fetch(`/web/api/v1/history/sessions/${TARGET_SESSION_ID}`);
                const result = await response.json();
                
                let output = '=== 会话详情API测试 ===\n';
                output += `Session ID: ${TARGET_SESSION_ID}\n`;
                output += `Status: ${response.status}\n`;
                output += `Response: ${JSON.stringify(result, null, 2)}`;
                
                showOutput('apiOutput', output);
                showStatus('会话详情API测试完成', response.ok ? 'success' : 'error');
            } catch (error) {
                showOutput('apiOutput', `会话详情API测试失败: ${error.message}`);
                showStatus('会话详情API测试失败', 'error');
            }
        }
        
        function clearAllData() {
            if (confirm('确定要清空所有历史记录数据吗？')) {
                localStorage.removeItem('genie_history_sessions');
                localStorage.removeItem('genie_history_messages');
                localStorage.removeItem('genie_history_files');
                showStatus('所有数据已清空', 'warning');
                
                showOutput('checkOutput', '所有 localStorage 数据已清空');
            }
        }
        
        // 页面加载时自动检查数据
        window.onload = function() {
            showStatus('页面加载完成，开始检查数据...', 'info');
            checkLocalStorageData();
        };
    </script>
</body>
</html>