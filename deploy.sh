#!/bin/bash

# JoyAgent-JDGenie 一键部署脚本 (Vercel + Railway方案)
# 使用方法: ./deploy.sh

set -e

echo "🚀 JoyAgent-JDGenie 一键部署脚本"
echo "=================================="

# 检查必要的工具
check_dependencies() {
    echo "📋 检查依赖工具..."
    
    if ! command -v git &> /dev/null; then
        echo "❌ Git 未安装，请先安装 Git"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装，请先安装 npm"
        exit 1
    fi
    
    echo "✅ 依赖检查完成"
}

# 安装Vercel CLI
install_vercel_cli() {
    echo "📦 安装 Vercel CLI..."
    if ! command -v vercel &> /dev/null; then
        npm install -g vercel
        echo "✅ Vercel CLI 安装完成"
    else
        echo "✅ Vercel CLI 已安装"
    fi
}

# 配置环境变量
setup_environment() {
    echo "⚙️  配置环境变量..."
    
    # 创建环境变量配置文件
    cat > .env.deployment << EOF
# 请填写以下环境变量
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=your_openai_base_url_here
SERPER_SEARCH_API_KEY=your_serper_api_key_here

# Railway服务URL (部署后填写)
RAILWAY_BACKEND_URL=https://your-backend.railway.app
RAILWAY_TOOL_URL=https://your-tool.railway.app
RAILWAY_MCP_URL=https://your-mcp.railway.app
EOF
    
    echo "📝 已创建 .env.deployment 文件"
    echo "⚠️  请编辑 .env.deployment 文件，填写正确的API密钥"
    echo ""
    read -p "按回车键继续 (确保已填写环境变量)..."
}

# 部署前端到Vercel
deploy_frontend() {
    echo "🌐 部署前端到 Vercel..."
    
    # 检查是否已登录Vercel
    if ! vercel whoami &> /dev/null; then
        echo "🔐 请登录 Vercel..."
        vercel login
    fi
    
    # 部署到Vercel
    echo "📤 开始部署前端..."
    cd ui
    vercel --prod
    cd ..
    
    echo "✅ 前端部署完成"
}

# 生成Railway部署指南
generate_railway_guide() {
    echo "📋 生成 Railway 部署指南..."
    
    cat > railway-deployment-guide.md << EOF
# Railway 后端服务部署指南

## 1. 注册Railway账号
访问 https://railway.app 注册账号

## 2. 创建项目

### 2.1 后端API服务
1. 在Railway创建新项目
2. 连接GitHub仓库
3. 选择 Dockerfile.backend
4. 设置环境变量：
   - OPENAI_API_KEY: $(grep OPENAI_API_KEY .env.deployment | cut -d'=' -f2)
   - OPENAI_BASE_URL: $(grep OPENAI_BASE_URL .env.deployment | cut -d'=' -f2)
5. 部署完成后记录URL

### 2.2 工具服务
1. 在Railway创建新项目
2. 连接GitHub仓库
3. 选择 Dockerfile.tool
4. 设置环境变量：
   - OPENAI_API_KEY: $(grep OPENAI_API_KEY .env.deployment | cut -d'=' -f2)
   - SERPER_SEARCH_API_KEY: $(grep SERPER_SEARCH_API_KEY .env.deployment | cut -d'=' -f2)
5. 部署完成后记录URL

### 2.3 MCP客户端服务
1. 在Railway创建新项目
2. 连接GitHub仓库
3. 选择 Dockerfile.client
4. 设置环境变量：
   - OPENAI_API_KEY: $(grep OPENAI_API_KEY .env.deployment | cut -d'=' -f2)
5. 部署完成后记录URL

## 3. 更新Vercel配置
部署完Railway服务后，需要更新Vercel项目的环境变量：
1. 访问 Vercel Dashboard
2. 选择你的项目
3. 进入 Settings > Environment Variables
4. 添加以下变量：
   - VITE_API_BASE_URL: Railway后端API的URL
   - VITE_TOOL_SERVICE_URL: Railway工具服务的URL
   - VITE_MCP_SERVICE_URL: Railway MCP服务的URL
5. 重新部署前端项目

## 4. 测试部署
访问Vercel提供的域名，测试应用功能是否正常。
EOF
    
    echo "✅ Railway部署指南已生成: railway-deployment-guide.md"
}

# 主函数
main() {
    echo "开始部署流程..."
    echo ""
    
    check_dependencies
    echo ""
    
    install_vercel_cli
    echo ""
    
    setup_environment
    echo ""
    
    deploy_frontend
    echo ""
    
    generate_railway_guide
    echo ""
    
    echo "🎉 部署脚本执行完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 按照 railway-deployment-guide.md 部署后端服务"
    echo "2. 更新Vercel环境变量"
    echo "3. 测试应用功能"
    echo ""
    echo "📚 详细文档请查看 deployment/ 目录"
}

# 执行主函数
main