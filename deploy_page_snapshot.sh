#!/bin/bash

# 页面快照功能部署脚本
# 用于快速部署页面快照功能的所有组件

echo "开始部署页面快照功能..."

# 检查是否在项目根目录
if [ ! -f "package.json" ] && [ ! -f "ui/package.json" ]; then
    echo "❌ 请在项目根目录执行此脚本"
    exit 1
fi

# 1. 创建前端目录结构
echo "📁 创建前端目录结构..."
mkdir -p ui/src/types
mkdir -p ui/src/utils
mkdir -p ui/src/services
mkdir -p ui/src/hooks
mkdir -p ui/src/components/HistoryReplayV2

# 2. 创建后端目录结构
echo "📁 创建后端目录结构..."
mkdir -p genie-backend/src/main/java/com/jd/genie/controller
mkdir -p genie-backend/src/main/java/com/jd/genie/service
mkdir -p genie-backend/src/main/java/com/jd/genie/model
mkdir -p genie-backend/src/main/java/com/jd/genie/repo
mkdir -p genie-backend/src/main/resources/db/migration

# 3. 检查文件是否存在
echo "🔍 检查必需文件..."

FRONTEND_FILES=(
    "ui/src/types/pageSnapshot.ts"
    "ui/src/utils/pageSnapshotCapture.ts"
    "ui/src/services/pageSnapshotService.ts"
    "ui/src/hooks/usePageSnapshotCapture.ts"
    "ui/src/components/HistoryReplayV2/index.tsx"
)

BACKEND_FILES=(
    "genie-backend/src/main/java/com/jd/genie/controller/PageSnapshotController.java"
    "genie-backend/src/main/java/com/jd/genie/service/PageSnapshotService.java"
    "genie-backend/src/main/java/com/jd/genie/model/PageSnapshotRecord.java"
    "genie-backend/src/main/java/com/jd/genie/repo/PageSnapshotRepository.java"
    "genie-backend/src/main/resources/db/migration/V3__Create_page_snapshot_table.sql"
)

MISSING_FILES=()

for file in "${FRONTEND_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

for file in "${BACKEND_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "❌ 以下文件缺失，请先创建这些文件："
    for file in "${MISSING_FILES[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

echo "✅ 所有必需文件都存在"

# 4. 检查前端依赖
echo "📦 检查前端依赖..."
cd ui

# 检查package.json中是否有必需的依赖
REQUIRED_DEPS=("antd" "ahooks" "@ant-design/icons")
MISSING_DEPS=()

for dep in "${REQUIRED_DEPS[@]}"; do
    if ! npm list "$dep" > /dev/null 2>&1; then
        MISSING_DEPS+=("$dep")
    fi
done

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    echo "📦 安装缺失的依赖..."
    npm install "${MISSING_DEPS[@]}"
fi

cd ..

# 5. 验证后端Java文件语法
echo "☕ 验证后端Java文件..."
cd genie-backend

# 检查Java文件是否有语法错误
for file in "${BACKEND_FILES[@]}"; do
    if [[ "$file" == *.java ]]; then
        if ! javac -cp ".:lib/*" -d /tmp "$file" 2>/dev/null; then
            echo "⚠️  警告: $file 可能存在语法错误"
        fi
    fi
done

cd ..

# 6. 创建配置文件备份
echo "💾 备份配置文件..."
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

if [ -f "ui/.env" ]; then
    cp "ui/.env" "$BACKUP_DIR/"
fi

if [ -f "genie-backend/src/main/resources/application.yml" ]; then
    cp "genie-backend/src/main/resources/application.yml" "$BACKUP_DIR/"
fi

# 7. 检查数据库迁移
echo "🗄️  检查数据库迁移..."
if [ -f "genie-backend/src/main/resources/db/migration/V3__Create_page_snapshot_table.sql" ]; then
    echo "✅ 数据库迁移文件存在"
else
    echo "❌ 数据库迁移文件缺失"
    exit 1
fi

# 8. 生成部署报告
echo "📋 生成部署报告..."
cat > deployment_report.md << EOF
# 页面快照功能部署报告

## 部署时间
$(date)

## 部署状态
✅ 部署成功

## 已部署文件

### 前端文件
$(for file in "${FRONTEND_FILES[@]}"; do echo "- $file"; done)

### 后端文件
$(for file in "${BACKEND_FILES[@]}"; do echo "- $file"; done)

## 下一步操作

### 1. 启动后端服务
\`\`\`bash
cd genie-backend
./gradlew bootRun
\`\`\`

### 2. 启动前端服务
\`\`\`bash
cd ui
npm start
\`\`\`

### 3. 验证功能
1. 创建新的对话任务
2. 等待任务完成，验证自动保存功能
3. 点击"保存记录"按钮，验证手动保存功能
4. 访问历史页面，验证回放功能

### 4. 监控日志
- 后端日志：检查快照保存API调用
- 前端控制台：检查快照捕获和保存过程
- 数据库：验证page_snapshot表数据

## 配置文件备份
配置文件已备份到: $BACKUP_DIR/

## 故障排除

### 如果自动保存不工作
1. 检查usePageSnapshotCapture Hook是否正确集成
2. 验证任务状态变化监听
3. 查看浏览器控制台错误信息

### 如果历史回放显示异常
1. 检查快照数据完整性
2. 验证ActionView组件props传递
3. 确认快照加载API正常工作

### 如果数据库操作失败
1. 检查数据库连接配置
2. 验证表结构是否正确创建
3. 查看后端日志中的SQL错误

## 联系信息
如有问题，请查看 PAGE_SNAPSHOT_IMPLEMENTATION.md 文档
EOF

echo "📋 部署报告已生成: deployment_report.md"

# 9. 最终检查
echo "🔍 执行最终检查..."

# 检查前端TypeScript编译
echo "   检查前端TypeScript..."
cd ui
if command -v tsc > /dev/null; then
    if ! tsc --noEmit --skipLibCheck; then
        echo "⚠️  前端TypeScript编译检查发现问题"
    else
        echo "✅ 前端TypeScript检查通过"
    fi
else
    echo "⚠️  未找到TypeScript编译器，跳过检查"
fi
cd ..

# 检查后端编译
echo "   检查后端编译..."
cd genie-backend
if command -v ./gradlew > /dev/null; then
    if ! ./gradlew compileJava --quiet; then
        echo "⚠️  后端Java编译检查发现问题"
    else
        echo "✅ 后端Java编译检查通过"
    fi
else
    echo "⚠️  未找到Gradle，跳过编译检查"
fi
cd ..

echo ""
echo "🎉 页面快照功能部署完成！"
echo ""
echo "📖 请查看以下文档："
echo "   - PAGE_SNAPSHOT_IMPLEMENTATION.md (实现方案详细说明)"
echo "   - deployment_report.md (部署报告)"
echo ""
echo "🚀 下一步："
echo "   1. 启动后端服务: cd genie-backend && ./gradlew bootRun"
echo "   2. 启动前端服务: cd ui && npm start"
echo "   3. 测试页面快照功能"
echo ""
echo "💡 提示：如果遇到问题，请查看deployment_report.md中的故障排除部分"
