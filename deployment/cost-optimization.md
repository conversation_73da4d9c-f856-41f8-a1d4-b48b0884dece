# 成本优化配置指南

## 免费额度最大化利用

### Vercel 免费额度
- **带宽**: 100GB/月
- **构建时间**: 6000分钟/月
- **Serverless函数**: 100GB-小时/月
- **域名**: 无限制

**优化策略:**
1. 启用静态资源缓存
2. 压缩图片和资源
3. 使用CDN加速
4. 减少不必要的重新部署

### Railway 免费额度优化
- **免费额度**: $5/月
- **资源限制**: 512MB RAM, 1GB磁盘

**优化策略:**
1. 合并轻量级服务
2. 使用轻量级基础镜像
3. 优化Docker镜像大小
4. 实现服务休眠机制

## 成本控制配置

### 1. 轻量级Dockerfile优化

```dockerfile
# 多阶段构建减少镜像大小
FROM maven:3.8-openjdk-17-slim as builder
WORKDIR /app
COPY genie-backend/pom.xml .
COPY genie-backend/src ./src
RUN mvn clean package -DskipTests -Dmaven.javadoc.skip=true

# 使用更小的运行时镜像
FROM openjdk:17-jre-alpine
WORKDIR /app
COPY --from=builder /app/target/*.jar app.jar

# 优化JVM参数
ENV JAVA_OPTS="-Xms128m -Xmx256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
EXPOSE 8080
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 2. 服务合并配置

```yaml
# docker-compose.yml - 合并轻量级服务
version: '3.8'
services:
  backend-combined:
    build:
      context: .
      dockerfile: Dockerfile.combined
    ports:
      - "8080:8080"
      - "1601:1601"
      - "1602:1602"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SERPER_SEARCH_API_KEY=${SERPER_SEARCH_API_KEY}
```

### 3. 资源监控配置

```bash
#!/bin/bash
# monitor-resources.sh - 资源使用监控脚本

# 检查内存使用
check_memory() {
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')
    echo "内存使用率: ${MEMORY_USAGE}%"
    
    if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
        echo "⚠️ 内存使用率过高，建议优化"
    fi
}

# 检查磁盘使用
check_disk() {
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    echo "磁盘使用率: ${DISK_USAGE}%"
    
    if [ "$DISK_USAGE" -gt 80 ]; then
        echo "⚠️ 磁盘使用率过高，建议清理"
    fi
}

check_memory
check_disk
```

## 免费替代方案

### 1. 完全免费的Cloudflare方案

**成本**: $0/月
**限制**: 需要重构代码

```typescript
// workers/combined/src/index.ts
import { Hono } from 'hono'
import { cors } from 'hono/cors'

const app = new Hono()
app.use('*', cors())

// 合并所有API到一个Worker
app.route('/api', apiRoutes)
app.route('/tools', toolRoutes)
app.route('/mcp', mcpRoutes)

export default app
```

### 2. 混合部署方案

**前端**: Cloudflare Pages (免费)
**后端**: Railway (最小配置)
**数据库**: PlanetScale (免费额度)

**预估成本**: $5-10/月

## 成本监控和报警

### 1. Railway成本监控

```javascript
// railway-cost-monitor.js
const RAILWAY_API_TOKEN = process.env.RAILWAY_API_TOKEN;
const WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;

async function checkUsage() {
    const response = await fetch('https://backboard.railway.app/graphql', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${RAILWAY_API_TOKEN}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: `
                query {
                    me {
                        usage {
                            current
                            limit
                        }
                    }
                }
            `
        })
    });
    
    const data = await response.json();
    const usage = data.data.me.usage;
    const usagePercent = (usage.current / usage.limit) * 100;
    
    if (usagePercent > 80) {
        await sendAlert(`Railway使用率达到 ${usagePercent.toFixed(2)}%`);
    }
}

async function sendAlert(message) {
    await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: message })
    });
}

// 每小时检查一次
setInterval(checkUsage, 3600000);
```

### 2. Vercel使用量监控

```javascript
// vercel-usage-monitor.js
const VERCEL_TOKEN = process.env.VERCEL_TOKEN;
const TEAM_ID = process.env.VERCEL_TEAM_ID;

async function checkVercelUsage() {
    const response = await fetch(`https://api.vercel.com/v1/teams/${TEAM_ID}/usage`, {
        headers: {
            'Authorization': `Bearer ${VERCEL_TOKEN}`
        }
    });
    
    const usage = await response.json();
    
    // 检查带宽使用
    const bandwidthPercent = (usage.bandwidth.used / usage.bandwidth.limit) * 100;
    if (bandwidthPercent > 80) {
        console.log(`⚠️ Vercel带宽使用率: ${bandwidthPercent.toFixed(2)}%`);
    }
}
```

## 自动化成本优化

### 1. 自动休眠脚本

```bash
#!/bin/bash
# auto-sleep.sh - 自动休眠非活跃服务

# 检查服务活跃度
check_activity() {
    local service_url=$1
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$service_url/health")
    
    if [ "$response" = "200" ]; then
        echo "服务活跃: $service_url"
        return 0
    else
        echo "服务非活跃: $service_url"
        return 1
    fi
}

# 休眠服务
sleep_service() {
    local service_name=$1
    echo "休眠服务: $service_name"
    # 这里可以调用Railway API来暂停服务
}

# 主逻辑
main() {
    if ! check_activity "$TOOL_SERVICE_URL"; then
        sleep_service "tool-service"
    fi
    
    if ! check_activity "$MCP_SERVICE_URL"; then
        sleep_service "mcp-service"
    fi
}

main
```

### 2. 智能缓存策略

```typescript
// cache-strategy.ts
export class SmartCache {
    private cache = new Map();
    private readonly TTL = 3600000; // 1小时
    
    async get(key: string, fetcher: () => Promise<any>) {
        const cached = this.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < this.TTL) {
            return cached.data;
        }
        
        const data = await fetcher();
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
        
        return data;
    }
    
    // 定期清理过期缓存
    cleanup() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > this.TTL) {
                this.cache.delete(key);
            }
        }
    }
}
```

## 总结

通过以上优化策略，可以将部署成本控制在最低水平：

1. **最低成本方案**: Cloudflare完全免费 ($0/月)
2. **平衡方案**: Vercel + Railway优化 ($5-10/月)
3. **标准方案**: Vercel + Railway标准 ($15/月)

选择合适的方案取决于你的技术能力和时间投入。