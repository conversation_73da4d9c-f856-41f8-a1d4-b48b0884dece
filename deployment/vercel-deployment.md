# Vercel + 免费后端服务部署方案

## 方案概述
- **前端**: Vercel (免费)
- **后端API**: Railway/Render (免费额度)
- **Python服务**: Railway/Render (免费额度)
- **数据库**: SQLite (文件数据库，免费)

## 1. Vercel前端部署

### 1.1 优化vercel.json配置
```json
{
  "buildCommand": "cd ui && npm install && npm run build",
  "outputDirectory": "ui/dist",
  "installCommand": "cd ui && npm install",
  "devCommand": "cd ui && npm run dev",
  "framework": "vite",
  "functions": {
    "ui/dist/**": {
      "maxDuration": 10
    }
  },
  "rewrites": [
    {
      "source": "/web/(.*)",
      "destination": "https://your-backend-url.railway.app/web/$1"
    },
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

### 1.2 环境变量配置
在Vercel项目设置中添加：
```
VITE_API_BASE_URL=https://your-backend-url.railway.app
VITE_TOOL_SERVICE_URL=https://your-tool-service.railway.app
VITE_MCP_SERVICE_URL=https://your-mcp-service.railway.app
```

## 2. Railway后端部署

### 2.1 Java后端服务
创建 `railway.json`:
```json
{
  "build": {
    "builder": "DOCKERFILE",
    "dockerfilePath": "Dockerfile.backend"
  },
  "deploy": {
    "startCommand": "java -jar target/genie-backend-0.0.1-SNAPSHOT.jar",
    "healthcheckPath": "/actuator/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### 2.2 Python工具服务
创建 `railway-tool.json`:
```json
{
  "build": {
    "builder": "DOCKERFILE",
    "dockerfilePath": "Dockerfile.tool"
  },
  "deploy": {
    "startCommand": "cd genie-tool && uv run python server.py",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100
  }
}
```

### 2.3 Python MCP客户端
创建 `railway-client.json`:
```json
{
  "build": {
    "builder": "DOCKERFILE", 
    "dockerfilePath": "Dockerfile.client"
  },
  "deploy": {
    "startCommand": "cd genie-client && python server.py",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100
  }
}
```

## 3. 分离式Dockerfile

### 3.1 后端Dockerfile
```dockerfile
# Dockerfile.backend
FROM maven:3.8-openjdk-17 as builder
WORKDIR /app
COPY genie-backend/pom.xml .
COPY genie-backend/src ./src
RUN mvn clean package -DskipTests

FROM openjdk:17-jre-slim
WORKDIR /app
COPY --from=builder /app/target/*.jar app.jar
EXPOSE 8080
CMD ["java", "-jar", "app.jar"]
```

### 3.2 工具服务Dockerfile  
```dockerfile
# Dockerfile.tool
FROM python:3.11-slim
WORKDIR /app
RUN pip install uv
COPY genie-tool/ .
RUN uv venv .venv && \
    . .venv/bin/activate && \
    uv pip install .
EXPOSE 1601
CMD [".venv/bin/python", "server.py"]
```

### 3.3 MCP客户端Dockerfile
```dockerfile
# Dockerfile.client
FROM python:3.11-slim
WORKDIR /app
RUN pip install uv
COPY genie-client/ .
RUN uv venv .venv && \
    . .venv/bin/activate && \
    uv pip install -r pyproject.toml
EXPOSE 1602
CMD [".venv/bin/python", "server.py"]
```

## 4. 部署步骤

### 4.1 准备工作
1. 注册Vercel账号
2. 注册Railway账号
3. 准备GitHub仓库

### 4.2 后端服务部署
1. 在Railway创建3个项目：
   - genie-backend
   - genie-tool  
   - genie-client

2. 连接GitHub仓库并配置构建

3. 设置环境变量：
   ```
   OPENAI_API_KEY=your_openai_key
   OPENAI_BASE_URL=your_openai_base_url
   SERPER_SEARCH_API_KEY=your_serper_key
   ```

### 4.3 前端部署
1. 在Vercel导入项目
2. 设置构建配置指向ui目录
3. 配置环境变量指向Railway服务URL
4. 部署

## 5. 成本分析
- **Vercel**: 免费额度足够个人使用
- **Railway**: 每月$5免费额度，3个服务约$15/月
- **总成本**: 约$15/月

## 6. 优化建议
1. 使用CDN加速静态资源
2. 启用Gzip压缩
3. 配置缓存策略
4. 监控服务健康状态