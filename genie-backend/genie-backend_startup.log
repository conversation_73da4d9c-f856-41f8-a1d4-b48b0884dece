
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.2)[0;39m

[2m2025-08-09T17:19:28.199+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mcom.jd.genie.GenieApplication           [0;39m [2m:[0;39m Starting GenieApplication v0.0.1-SNAPSHOT using Java 22.0.1 with PID 26595 (/Users/<USER>/git_project_vscode/04-mutil-agents/joyagent-jdgenie/genie-backend/target/genie-backend/lib/genie-backend-0.0.1-SNAPSHOT.jar started by charlieliu in /Users/<USER>/git_project_vscode/04-mutil-agents/joyagent-jdgenie/genie-backend)
[2m2025-08-09T17:19:28.201+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mcom.jd.genie.GenieApplication           [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-09T17:19:28.779+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8080 (http)
[2m2025-08-09T17:19:28.785+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-09T17:19:28.785+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.18]
[2m2025-08-09T17:19:28.810+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-09T17:19:28.811+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 588 ms
[2m2025-08-09T17:19:29.733+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8080 (http) with context path ''
[2m2025-08-09T17:19:29.743+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mcom.jd.genie.GenieApplication           [0;39m [2m:[0;39m Started GenieApplication in 1.76 seconds (process running for 1.929)
[2m2025-08-09T17:19:29.984+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [nio-8080-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-09T17:19:29.984+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [nio-8080-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-09T17:19:29.985+08:00[0;39m [32m INFO[0;39m [35m26595[0;39m [2m---[0;39m [2m[genie-backend] [nio-8080-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 1 ms
