
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.2)[0;39m

[2m2025-08-09T21:03:52.245+08:00[0;39m [32m INFO[0;39m [35m8155[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mcom.jd.genie.GenieApplication           [0;39m [2m:[0;39m Starting GenieApplication v0.0.1-SNAPSHOT using Java 22.0.1 with PID 8155 (/Users/<USER>/git_project_vscode/04-mutil-agents/joyagent-jdgenie/genie-backend/target/genie-backend/lib/genie-backend-0.0.1-SNAPSHOT.jar started by charlieliu in /Users/<USER>/git_project_vscode/04-mutil-agents/joyagent-jdgenie/genie-backend)
[2m2025-08-09T21:03:52.248+08:00[0;39m [32m INFO[0;39m [35m8155[0;39m [2m---[0;39m [2m[genie-backend] [           main][0;39m [2m[0;39m[36mcom.jd.genie.GenieApplication           [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
