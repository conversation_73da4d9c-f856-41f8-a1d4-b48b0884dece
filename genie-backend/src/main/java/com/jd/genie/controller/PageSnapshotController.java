package com.jd.genie.controller;

import com.jd.genie.service.PageSnapshotService;
import com.jd.genie.model.PageSnapshotRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 页面快照控制器
 * 处理页面状态快照的保存、加载和管理
 */
@RestController
@RequestMapping("/web/api/v1/snapshots")
public class PageSnapshotController {

    @Autowired
    private PageSnapshotService pageSnapshotService;

    /**
     * 保存页面快照
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> saveSnapshot(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> snapshotData = (Map<String, Object>) request.get("snapshot");

            if (snapshotData == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("message", "快照数据不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            String snapshotId = pageSnapshotService.saveSnapshot(snapshotData);

            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("code", 200);
            successResponse.put("data", Map.of(
                "snapshotId", snapshotId,
                "message", "快照保存成功"
            ));
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "保存快照失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 加载页面快照
     */
    @GetMapping("/{snapshotId}")
    public ResponseEntity<Map<String, Object>> loadSnapshot(@PathVariable String snapshotId) {
        try {
            PageSnapshotRecord snapshot = pageSnapshotService.getSnapshot(snapshotId);

            if (snapshot == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 404);
                errorResponse.put("message", "快照不存在");
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("code", 200);
            successResponse.put("data", Map.of(
                "snapshot", snapshot.getSnapshotData(),
                "metadata", Map.of(
                    "id", snapshot.getId(),
                    "sessionId", snapshot.getSessionId(),
                    "timestamp", snapshot.getTimestamp(),
                    "type", snapshot.getType(),
                    "isFinal", snapshot.getIsFinal()
                )
            ));
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "加载快照失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取会话的快照列表
     */
    @GetMapping("/session/{sessionId}")
    public ResponseEntity<Map<String, Object>> getSessionSnapshots(@PathVariable String sessionId) {
        try {
            List<Map<String, Object>> snapshots = pageSnapshotService.getSessionSnapshotList(sessionId);

            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("code", 200);
            successResponse.put("data", Map.of(
                "snapshots", snapshots,
                "total", snapshots.size()
            ));
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "获取快照列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 删除快照
     */
    @DeleteMapping("/{snapshotId}")
    public ResponseEntity<Map<String, Object>> deleteSnapshot(@PathVariable String snapshotId) {
        try {
            boolean success = pageSnapshotService.deleteSnapshot(snapshotId);

            Map<String, Object> response = new HashMap<>();
            if (success) {
                response.put("code", 200);
                response.put("message", "快照删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("code", 400);
                response.put("message", "快照删除失败");
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "删除快照失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 清理过期快照
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupExpiredSnapshots(@RequestBody Map<String, Object> request) {
        try {
            Long maxAge = request.get("maxAge") != null ?
                Long.valueOf(request.get("maxAge").toString()) :
                30L * 24 * 60 * 60 * 1000; // 默认30天

            int cleanedCount = pageSnapshotService.cleanupExpiredSnapshots(maxAge);

            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("code", 200);
            successResponse.put("data", Map.of(
                "cleanedCount", cleanedCount,
                "message", "清理完成"
            ));
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "清理快照失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取快照统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getSnapshotStats() {
        try {
            Map<String, Object> stats = pageSnapshotService.getSnapshotStats();

            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("code", 200);
            successResponse.put("data", stats);
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 批量删除快照
     */
    @PostMapping("/batch-delete")
    public ResponseEntity<Map<String, Object>> batchDeleteSnapshots(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> snapshotIds = (List<String>) request.get("snapshotIds");

            if (snapshotIds == null || snapshotIds.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("message", "快照ID列表不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            int deletedCount = pageSnapshotService.batchDeleteSnapshots(snapshotIds);

            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("code", 200);
            successResponse.put("data", Map.of(
                "deletedCount", deletedCount,
                "total", snapshotIds.size(),
                "message", "批量删除完成"
            ));
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "批量删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 验证快照数据完整性
     */
    @PostMapping("/{snapshotId}/validate")
    public ResponseEntity<Map<String, Object>> validateSnapshot(@PathVariable String snapshotId) {
        try {
            Map<String, Object> validation = pageSnapshotService.validateSnapshot(snapshotId);

            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("code", 200);
            successResponse.put("data", validation);
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "验证快照失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
