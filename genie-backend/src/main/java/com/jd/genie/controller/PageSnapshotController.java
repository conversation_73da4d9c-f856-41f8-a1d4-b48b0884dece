package com.jd.genie.controller;

import com.jd.genie.common.ApiResponse;
import com.jd.genie.service.PageSnapshotService;
import com.jd.genie.model.PageSnapshotRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 页面快照控制器
 * 处理页面状态快照的保存、加载和管理
 */
@RestController
@RequestMapping("/web/api/v1/snapshots")
public class PageSnapshotController {

    @Autowired
    private PageSnapshotService pageSnapshotService;

    /**
     * 保存页面快照
     */
    @PostMapping
    public ApiResponse<Map<String, Object>> saveSnapshot(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> snapshotData = (Map<String, Object>) request.get("snapshot");
            
            if (snapshotData == null) {
                return ApiResponse.error("快照数据不能为空");
            }

            String snapshotId = pageSnapshotService.saveSnapshot(snapshotData);
            
            return ApiResponse.success(Map.of(
                "snapshotId", snapshotId,
                "message", "快照保存成功"
            ));
            
        } catch (Exception e) {
            return ApiResponse.error("保存快照失败: " + e.getMessage());
        }
    }

    /**
     * 加载页面快照
     */
    @GetMapping("/{snapshotId}")
    public ApiResponse<Map<String, Object>> loadSnapshot(@PathVariable String snapshotId) {
        try {
            PageSnapshotRecord snapshot = pageSnapshotService.getSnapshot(snapshotId);
            
            if (snapshot == null) {
                return ApiResponse.error("快照不存在");
            }

            return ApiResponse.success(Map.of(
                "snapshot", snapshot.getSnapshotData(),
                "metadata", Map.of(
                    "id", snapshot.getId(),
                    "sessionId", snapshot.getSessionId(),
                    "timestamp", snapshot.getTimestamp(),
                    "type", snapshot.getType(),
                    "isFinal", snapshot.getIsFinal()
                )
            ));
            
        } catch (Exception e) {
            return ApiResponse.error("加载快照失败: " + e.getMessage());
        }
    }

    /**
     * 获取会话的快照列表
     */
    @GetMapping("/session/{sessionId}")
    public ApiResponse<Map<String, Object>> getSessionSnapshots(@PathVariable String sessionId) {
        try {
            List<Map<String, Object>> snapshots = pageSnapshotService.getSessionSnapshotList(sessionId);
            
            return ApiResponse.success(Map.of(
                "snapshots", snapshots,
                "total", snapshots.size()
            ));
            
        } catch (Exception e) {
            return ApiResponse.error("获取快照列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除快照
     */
    @DeleteMapping("/{snapshotId}")
    public ApiResponse<String> deleteSnapshot(@PathVariable String snapshotId) {
        try {
            boolean success = pageSnapshotService.deleteSnapshot(snapshotId);
            
            if (success) {
                return ApiResponse.success("快照删除成功");
            } else {
                return ApiResponse.error("快照删除失败");
            }
            
        } catch (Exception e) {
            return ApiResponse.error("删除快照失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期快照
     */
    @PostMapping("/cleanup")
    public ApiResponse<Map<String, Object>> cleanupExpiredSnapshots(@RequestBody Map<String, Object> request) {
        try {
            Long maxAge = request.get("maxAge") != null ? 
                Long.valueOf(request.get("maxAge").toString()) : 
                30L * 24 * 60 * 60 * 1000; // 默认30天

            int cleanedCount = pageSnapshotService.cleanupExpiredSnapshots(maxAge);
            
            return ApiResponse.success(Map.of(
                "cleanedCount", cleanedCount,
                "message", "清理完成"
            ));
            
        } catch (Exception e) {
            return ApiResponse.error("清理快照失败: " + e.getMessage());
        }
    }

    /**
     * 获取快照统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getSnapshotStats() {
        try {
            Map<String, Object> stats = pageSnapshotService.getSnapshotStats();
            return ApiResponse.success(stats);
            
        } catch (Exception e) {
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除快照
     */
    @PostMapping("/batch-delete")
    public ApiResponse<Map<String, Object>> batchDeleteSnapshots(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> snapshotIds = (List<String>) request.get("snapshotIds");
            
            if (snapshotIds == null || snapshotIds.isEmpty()) {
                return ApiResponse.error("快照ID列表不能为空");
            }

            int deletedCount = pageSnapshotService.batchDeleteSnapshots(snapshotIds);
            
            return ApiResponse.success(Map.of(
                "deletedCount", deletedCount,
                "total", snapshotIds.size(),
                "message", "批量删除完成"
            ));
            
        } catch (Exception e) {
            return ApiResponse.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 验证快照数据完整性
     */
    @PostMapping("/{snapshotId}/validate")
    public ApiResponse<Map<String, Object>> validateSnapshot(@PathVariable String snapshotId) {
        try {
            Map<String, Object> validation = pageSnapshotService.validateSnapshot(snapshotId);
            return ApiResponse.success(validation);
            
        } catch (Exception e) {
            return ApiResponse.error("验证快照失败: " + e.getMessage());
        }
    }
}
