package com.jd.genie.history;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Statement;

@Slf4j
@Component
@RequiredArgsConstructor
public class HistoryConfig {
    private final DataSource dataSource;

    @PostConstruct
    public void initTables() {
        try (Connection conn = dataSource.getConnection(); Statement st = conn.createStatement()) {
            st.execute("CREATE TABLE IF NOT EXISTS session (" +
                    "session_id TEXT PRIMARY KEY, " +
                    "title TEXT, status TEXT, create_time INTEGER, update_time INTEGER, summary TEXT, product_type TEXT, tags TEXT)");
            st.execute("CREATE TABLE IF NOT EXISTS message (" +
                    "id TEXT PRIMARY KEY, session_id TEXT, role TEXT, content TEXT, timestamp INTEGER, " +
                    "thought TEXT, plan_thought TEXT, tool_thought TEXT, task_status INTEGER, result_map_json TEXT)");
            st.execute("CREATE TABLE IF NOT EXISTS event (" +
                    "id TEXT PRIMARY KEY, session_id TEXT, message_id TEXT, message_type TEXT, message_order INTEGER, " +
                    "task_id TEXT, task_order INTEGER, is_final INTEGER, payload_json TEXT, create_time INTEGER)");
            st.execute("CREATE TABLE IF NOT EXISTS file_ref (" +
                    "id TEXT PRIMARY KEY, session_id TEXT, file_name TEXT, download_url TEXT, preview_url TEXT, size INTEGER, file_type TEXT, create_time INTEGER)");

            st.execute("CREATE INDEX IF NOT EXISTS idx_message_session ON message(session_id)");
            st.execute("CREATE INDEX IF NOT EXISTS idx_event_session ON event(session_id)");
            st.execute("CREATE INDEX IF NOT EXISTS idx_file_session ON file_ref(session_id)");
        } catch (Exception e) {
            log.error("init history tables error", e);
        }
    }
}

