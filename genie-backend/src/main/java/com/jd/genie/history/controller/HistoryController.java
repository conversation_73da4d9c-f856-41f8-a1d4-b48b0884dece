package com.jd.genie.history.controller;

import com.jd.genie.history.model.EventRecord;
import com.jd.genie.history.model.FileRefRecord;
import com.jd.genie.history.model.MessageRecord;
import com.jd.genie.history.model.SessionRecord;
import com.jd.genie.history.service.HistoryService;
import com.jd.genie.history.service.HistoryValidationService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/web/api/v1/history")
@RequiredArgsConstructor
public class HistoryController {
    private final HistoryService service;
    private final HistoryValidationService validationService;

    @GetMapping("/sessions")
    public Map<String, Object> listSessions(@RequestParam(required = false) String keyword,
                                            @RequestParam(required = false) String status,
                                            @RequestParam(required = false) Long start,
                                            @RequestParam(required = false) Long end,
                                            @RequestParam(required = false) String tags,
                                            @RequestParam(required = false) String sortBy,
                                            @RequestParam(required = false) String sortOrder,
                                            @RequestParam(defaultValue = "1") int page,
                                            @RequestParam(defaultValue = "20") int pageSize) {
        List<SessionRecord> list = service.listSessions(keyword, status, start, end, tags, sortBy, sortOrder, page, pageSize);
        Map<String, Object> resp = new HashMap<>();
        resp.put("code", 200);
        resp.put("message", "success");
        resp.put("data", list);
        return resp;
    }

    @GetMapping("/sessions/{sessionId}")
    public Map<String, Object> getSessionDetail(@PathVariable String sessionId) {
        SessionRecord session = service.getSession(sessionId);
        List<MessageRecord> messages = service.listMessages(sessionId);
        List<FileRefRecord> files = service.listFiles(sessionId);
        Map<String, Object> resp = new HashMap<>();
        resp.put("code", 200);
        resp.put("message", "success");
        Map<String, Object> data = new HashMap<>();
        data.put("session", session);
        data.put("messages", messages);
        data.put("files", files);
        resp.put("data", data);
        return resp;
    }

    @GetMapping("/sessions/{sessionId}/events")
    public Map<String, Object> getSessionEvents(@PathVariable String sessionId) {
        List<EventRecord> events = service.listEvents(sessionId);
        Map<String, Object> resp = new HashMap<>();
        resp.put("code", 200);
        resp.put("message", "success");
        resp.put("data", events);
        return resp;
    }

    // 批量导入历史数据（sessions/messages/files/events），用于从本地导入
    @PostMapping("/import")
    public Map<String, Object> importHistory(@RequestBody Map<String, Object> body) {
        // 期望结构：{ sessions: SessionRecord[], messages: MessageRecord[], files: FileRefRecord[], events: EventRecord[] }
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> sessions = (List<Map<String, Object>>) body.getOrDefault("sessions", List.of());
            for (Map<String, Object> s : sessions) {
                SessionRecord rec = SessionRecord.builder()
                        .sessionId(String.valueOf(s.get("sessionId")))
                        .title((String) s.get("title"))
                        .status((String) s.get("status"))
                        .summary((String) s.get("summary"))
                        .productType((String) s.get("productType"))
                        .tags(s.get("tags") == null ? null : String.join(",", ((List<?>) s.get("tags")).stream().map(String::valueOf).toList()))
                        .createTime(s.get("createTime") == null ? null : Long.valueOf(String.valueOf(s.get("createTime"))))
                        .updateTime(s.get("updateTime") == null ? null : Long.valueOf(String.valueOf(s.get("updateTime"))))
                        .build();
                service.saveSessionRaw(rec);
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> messages = (List<Map<String, Object>>) body.getOrDefault("messages", List.of());
            for (Map<String, Object> m : messages) {
                MessageRecord rec = MessageRecord.builder()
                        .id((String) m.get("id"))
                        .sessionId((String) m.get("sessionId"))
                        .role((String) m.get("type"))
                        .content((String) m.get("content"))
                        .timestamp(m.get("timestamp") == null ? null : Long.valueOf(String.valueOf(m.get("timestamp"))))
                        .thought((String) m.get("thought"))
                        .planThought((String) m.get("planThought"))
                        .toolThought((String) m.get("toolThought"))
                        .taskStatus(m.get("taskStatus") == null ? null : Integer.valueOf(String.valueOf(m.get("taskStatus"))))
                        .resultMapJson(m.get("resultMap") == null ? null : com.alibaba.fastjson.JSON.toJSONString(m.get("resultMap")))
                        .build();
                service.saveMessageRaw(rec);
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> files = (List<Map<String, Object>>) body.getOrDefault("files", List.of());
            for (Map<String, Object> f : files) {
                FileRefRecord rec = FileRefRecord.builder()
                        .id((String) f.get("id"))
                        .sessionId((String) f.get("sessionId"))
                        .fileName((String) f.get("fileName"))
                        .downloadUrl((String) f.get("downloadUrl"))
                        .previewUrl((String) f.get("previewUrl"))
                        .size(f.get("fileSize") == null ? null : Long.valueOf(String.valueOf(f.get("fileSize"))))
                        .fileType((String) f.get("fileType"))
                        .createTime(f.get("createTime") == null ? null : Long.valueOf(String.valueOf(f.get("createTime"))))
                        .build();
                service.saveFile(rec.getSessionId(), rec.getFileName(), rec.getDownloadUrl(), rec.getPreviewUrl(), rec.getSize(), rec.getFileType());
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> events = (List<Map<String, Object>>) body.getOrDefault("events", List.of());
            for (Map<String, Object> e : events) {
                service.saveEvent(
                        (String) e.get("sessionId"),
                        (String) e.get("messageId"),
                        (String) e.get("messageType"),
                        e.get("messageOrder") == null ? null : Integer.valueOf(String.valueOf(e.get("messageOrder"))),
                        (String) e.get("taskId"),
                        e.get("taskOrder") == null ? null : Integer.valueOf(String.valueOf(e.get("taskOrder"))),
                        Boolean.valueOf(String.valueOf(e.getOrDefault("isFinal", false))),
                        e.get("payload")
                );
            }

            Map<String, Object> resp = new HashMap<>();
            resp.put("code", 200);
            resp.put("message", "success");
            resp.put("data", true);
            return resp;
        } catch (Exception ex) {
            Map<String, Object> resp = new HashMap<>();
            resp.put("code", 500);
            resp.put("message", ex.getMessage());
            return resp;
        }
    }

    // 数据验证端点
    @GetMapping("/sessions/{sessionId}/validate")
    public Map<String, Object> validateSession(@PathVariable String sessionId) {
        try {
            HistoryValidationService.ValidationResult result = validationService.validateSession(sessionId);
            Map<String, Object> resp = new HashMap<>();
            resp.put("code", 200);
            resp.put("message", "success");
            resp.put("data", result);
            return resp;
        } catch (Exception ex) {
            Map<String, Object> resp = new HashMap<>();
            resp.put("code", 500);
            resp.put("message", ex.getMessage());
            return resp;
        }
    }

    @PostMapping("/validate/batch")
    public Map<String, Object> validateMultipleSessions(@RequestBody Map<String, Object> body) {
        try {
            @SuppressWarnings("unchecked")
            List<String> sessionIds = (List<String>) body.get("sessionIds");
            if (sessionIds == null || sessionIds.isEmpty()) {
                Map<String, Object> resp = new HashMap<>();
                resp.put("code", 400);
                resp.put("message", "sessionIds is required");
                return resp;
            }

            Map<String, HistoryValidationService.ValidationResult> results = validationService.validateMultipleSessions(sessionIds);
            Map<String, Object> resp = new HashMap<>();
            resp.put("code", 200);
            resp.put("message", "success");
            resp.put("data", results);
            return resp;
        } catch (Exception ex) {
            Map<String, Object> resp = new HashMap<>();
            resp.put("code", 500);
            resp.put("message", ex.getMessage());
            return resp;
        }
    }
}

