package com.jd.genie.history.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventRecord {
    private String id;
    private String sessionId;
    private String messageId;
    private String messageType; // plan/task/tool_thought/.../result/agent_stream/heartbeat
    private Integer messageOrder;
    private String taskId;
    private Integer taskOrder;
    private Boolean isFinal;
    private String payloadJson; // raw JSON of event payload
    private Long createTime;
}

