package com.jd.genie.history.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageRecord {
    private String id;
    private String sessionId;
    private String role; // user/assistant/system
    private String content;
    private Long timestamp;
    private String thought;
    private String planThought;
    private String toolThought;
    private Integer taskStatus; // 1 running, 2 success, 3 error
    private String resultMapJson; // JSON string for extra structured data
}

