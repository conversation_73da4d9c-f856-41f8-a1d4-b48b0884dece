package com.jd.genie.history.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionRecord {
    private String sessionId;
    private String title;
    private String status; // running/completed/error
    private Long createTime;
    private Long updateTime;
    private String summary;
    private String productType;
    private String tags; // comma separated
}

