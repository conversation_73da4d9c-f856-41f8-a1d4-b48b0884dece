package com.jd.genie.history.repo;

import com.jd.genie.history.model.*;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class HistoryRepository {
    private final JdbcTemplate jdbcTemplate;

    public void upsertSession(SessionRecord s) {
        jdbcTemplate.update("INSERT INTO session(session_id, title, status, create_time, update_time, summary, product_type, tags) " +
                        "VALUES(?,?,?,?,?,?,?,?) ON CONFLICT(session_id) DO UPDATE SET title=?, status=?, update_time=?, summary=?, product_type=?, tags=?",
                s.getSessionId(), s.getTitle(), s.getStatus(), s.getCreateTime(), s.getUpdateTime(), s.getSummary(), s.getProductType(), s.getTags(),
                s.getTitle(), s.getStatus(), s.getUpdateTime(), s.getSummary(), s.getProductType(), s.getTags());
    }

    public void insertMessage(MessageRecord m) {
        jdbcTemplate.update("INSERT OR REPLACE INTO message(id, session_id, role, content, timestamp, thought, plan_thought, tool_thought, task_status, result_map_json) " +
                        "VALUES(?,?,?,?,?,?,?,?,?,?)",
                m.getId(), m.getSessionId(), m.getRole(), m.getContent(), m.getTimestamp(), m.getThought(), m.getPlanThought(), m.getToolThought(), m.getTaskStatus(), m.getResultMapJson());
    }

    public void insertEvent(EventRecord e) {
        jdbcTemplate.update("INSERT OR REPLACE INTO event(id, session_id, message_id, message_type, message_order, task_id, task_order, is_final, payload_json, create_time) " +
                        "VALUES(?,?,?,?,?,?,?,?,?,?)",
                e.getId(), e.getSessionId(), e.getMessageId(), e.getMessageType(), e.getMessageOrder(), e.getTaskId(), e.getTaskOrder(), Boolean.TRUE.equals(e.getIsFinal()) ? 1 : 0, e.getPayloadJson(), e.getCreateTime());
    }

    public void insertFile(FileRefRecord f) {
        jdbcTemplate.update("INSERT OR REPLACE INTO file_ref(id, session_id, file_name, download_url, preview_url, size, file_type, create_time) VALUES(?,?,?,?,?,?,?,?)",
                f.getId(), f.getSessionId(), f.getFileName(), f.getDownloadUrl(), f.getPreviewUrl(), f.getSize(), f.getFileType(), f.getCreateTime());
    }

    public List<SessionRecord> listSessions(String keyword, String status, Long start, Long end, String tags, String sortBy, String sortOrder, int offset, int limit) {
        StringBuilder sql = new StringBuilder("SELECT * FROM session WHERE 1=1");
        // 简化：用字符串拼接，实际可改为 NamedParameterJdbcTemplate
        if (keyword != null && !keyword.isEmpty()) sql.append(" AND (title LIKE '%" + keyword + "%' OR summary LIKE '%" + keyword + "%')");
        if (status != null && !status.isEmpty()) sql.append(" AND status='" + status + "'");
        if (start != null) sql.append(" AND create_time >= " + start);
        if (end != null) sql.append(" AND create_time <= " + end);
        if (tags != null && !tags.isEmpty()) sql.append(" AND tags LIKE '%" + tags + "%'");
        sql.append(" ORDER BY ").append(sortBy != null ? sortBy : "update_time").append(" ").append(sortOrder != null ? sortOrder : "DESC");
        sql.append(" LIMIT ").append(limit).append(" OFFSET ").append(offset);
        return jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(SessionRecord.class));
    }

    public List<MessageRecord> listMessages(String sessionId) {
        return jdbcTemplate.query("SELECT * FROM message WHERE session_id=? ORDER BY timestamp ASC",
                new BeanPropertyRowMapper<>(MessageRecord.class), sessionId);
    }

    public SessionRecord getSession(String sessionId) {
        List<SessionRecord> list = jdbcTemplate.query("SELECT * FROM session WHERE session_id=?",
                new BeanPropertyRowMapper<>(SessionRecord.class), sessionId);
        return list.isEmpty() ? null : list.get(0);
    }

    public List<FileRefRecord> listFiles(String sessionId) {
        return jdbcTemplate.query("SELECT * FROM file_ref WHERE session_id=? ORDER BY create_time ASC",
                new BeanPropertyRowMapper<>(FileRefRecord.class), sessionId);
    }

    public List<EventRecord> listEvents(String sessionId) {
        return jdbcTemplate.query("SELECT * FROM event WHERE session_id=? ORDER BY create_time ASC",
                new BeanPropertyRowMapper<>(EventRecord.class), sessionId);
    }
}

