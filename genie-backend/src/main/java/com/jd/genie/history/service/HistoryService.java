package com.jd.genie.history.service;

import com.alibaba.fastjson.JSON;
import com.jd.genie.history.model.*;
import com.jd.genie.history.repo.HistoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class HistoryService {
    private final HistoryRepository repo;

    public void upsertSession(String sessionId, String title, String status, String summary) {
        long now = System.currentTimeMillis();
        SessionRecord s = SessionRecord.builder()
                .sessionId(sessionId)
                .title(title)
                .status(status)
                .createTime(now)
                .updateTime(now)
                .summary(summary)
                .build();
        repo.upsertSession(s);
    }

    public void updateSessionStatus(String sessionId, String status, String summary) {
        SessionRecord s = SessionRecord.builder()
                .sessionId(sessionId)
                .status(status)
                .updateTime(System.currentTimeMillis())
                .summary(summary)
                .build();
        repo.upsertSession(s);
    }

    public void saveAssistantMessage(String sessionId, String content, String thought, String planThought, String toolThought, Integer taskStatus, Object resultMap) {
        MessageRecord m = MessageRecord.builder()
                .id(UUID.randomUUID().toString())
                .sessionId(sessionId)
                .role("assistant")
                .content(content)
                .timestamp(System.currentTimeMillis())
                .thought(thought)
                .planThought(planThought)
                .toolThought(toolThought)
                .taskStatus(taskStatus)
                .resultMapJson(resultMap == null ? null : JSON.toJSONString(resultMap))
                .build();
        repo.insertMessage(m);
    }

    public void saveUserMessage(String sessionId, String content) {
        MessageRecord m = MessageRecord.builder()
                .id(UUID.randomUUID().toString())
                .sessionId(sessionId)
                .role("user")
                .content(content)
                .timestamp(System.currentTimeMillis())
                .build();
        repo.insertMessage(m);
    }

    // 直接写入消息（用于导入）
    public void saveMessageRaw(MessageRecord m) {
        if (m.getId() == null) {
            m.setId(UUID.randomUUID().toString());
        }
        repo.insertMessage(m);
    }

    // 直接写入会话（用于导入，保留时间戳与标签等）
    public void saveSessionRaw(SessionRecord s) {
        if (s.getSessionId() == null) return;
        if (s.getUpdateTime() == null) s.setUpdateTime(System.currentTimeMillis());
        if (s.getCreateTime() == null) s.setCreateTime(s.getUpdateTime());
        repo.upsertSession(s);
    }

    public void saveEvent(String sessionId, String messageId, String messageType, Integer messageOrder, String taskId, Integer taskOrder, Boolean isFinal, Object payload) {
        EventRecord e = EventRecord.builder()
                .id(UUID.randomUUID().toString())
                .sessionId(sessionId)
                .messageId(messageId)
                .messageType(messageType)
                .messageOrder(messageOrder)
                .taskId(taskId)
                .taskOrder(taskOrder)
                .isFinal(isFinal)
                .payloadJson(payload == null ? null : JSON.toJSONString(payload))
                .createTime(System.currentTimeMillis())
                .build();
        repo.insertEvent(e);
    }

    public void saveFile(String sessionId, String fileName, String downloadUrl, String previewUrl, Long size, String fileType) {
        FileRefRecord f = FileRefRecord.builder()
                .id(UUID.randomUUID().toString())
                .sessionId(sessionId)
                .fileName(fileName)
                .downloadUrl(downloadUrl)
                .previewUrl(previewUrl)
                .size(size)
                .fileType(fileType)
                .createTime(System.currentTimeMillis())
                .build();
        repo.insertFile(f);
    }

    public List<SessionRecord> listSessions(String keyword, String status, Long start, Long end, String tags, String sortBy, String sortOrder, int page, int pageSize) {
        int offset = (Math.max(page, 1) - 1) * Math.max(pageSize, 1);
        return repo.listSessions(keyword, status, start, end, tags, sortBy, sortOrder, offset, pageSize);
    }

    public List<MessageRecord> listMessages(String sessionId) {
        return repo.listMessages(sessionId);
    }

    public SessionRecord getSession(String sessionId) {
        return repo.getSession(sessionId);
    }

    public List<FileRefRecord> listFiles(String sessionId) {
        return repo.listFiles(sessionId);
    }

    public List<EventRecord> listEvents(String sessionId) {
        return repo.listEvents(sessionId);
    }
}

