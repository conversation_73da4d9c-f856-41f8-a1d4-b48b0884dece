package com.jd.genie.history.service;

import com.alibaba.fastjson.JSON;
import com.jd.genie.history.model.EventRecord;
import com.jd.genie.history.model.MessageRecord;
import com.jd.genie.history.model.SessionRecord;
import com.jd.genie.history.repo.HistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 历史数据验证服务
 * 用于诊断和验证历史数据的完整性和一致性
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HistoryValidationService {
    
    private final HistoryRepository repo;
    
    /**
     * 验证会话的数据完整性
     */
    public ValidationResult validateSession(String sessionId) {
        ValidationResult result = new ValidationResult();
        result.sessionId = sessionId;
        
        try {
            // 检查会话基本信息
            SessionRecord session = repo.getSession(sessionId);
            if (session == null) {
                result.addError("Session not found: " + sessionId);
                return result;
            }
            
            // 检查消息数据
            List<MessageRecord> messages = repo.listMessages(sessionId);
            validateMessages(messages, result);
            
            // 检查事件数据
            List<EventRecord> events = repo.listEvents(sessionId);
            validateEvents(events, result);
            
            // 检查数据一致性
            validateDataConsistency(session, messages, events, result);
            
        } catch (Exception e) {
            result.addError("Validation failed: " + e.getMessage());
            log.error("Session validation failed for {}", sessionId, e);
        }
        
        return result;
    }
    
    /**
     * 验证消息数据
     */
    private void validateMessages(List<MessageRecord> messages, ValidationResult result) {
        if (messages.isEmpty()) {
            result.addWarning("No messages found");
            return;
        }
        
        boolean hasUserMessage = false;
        boolean hasAssistantMessage = false;
        
        for (MessageRecord msg : messages) {
            if (msg.getId() == null || msg.getId().isEmpty()) {
                result.addError("Message missing ID");
            }
            if (msg.getRole() == null || msg.getRole().isEmpty()) {
                result.addError("Message missing role");
            }
            if (msg.getContent() == null || msg.getContent().isEmpty()) {
                result.addWarning("Message has empty content: " + msg.getId());
            }
            if (msg.getTimestamp() == null) {
                result.addError("Message missing timestamp: " + msg.getId());
            }
            
            if ("user".equals(msg.getRole())) {
                hasUserMessage = true;
            } else if ("assistant".equals(msg.getRole())) {
                hasAssistantMessage = true;
            }
        }
        
        if (!hasUserMessage) {
            result.addWarning("No user message found");
        }
        if (!hasAssistantMessage) {
            result.addWarning("No assistant message found");
        }
        
        result.messageCount = messages.size();
    }
    
    /**
     * 验证事件数据
     */
    private void validateEvents(List<EventRecord> events, ValidationResult result) {
        if (events.isEmpty()) {
            result.addWarning("No events found");
            return;
        }
        
        Map<String, Integer> eventTypeCounts = new HashMap<>();
        int eventsWithoutPayload = 0;
        int eventsWithoutTaskId = 0;
        int eventsWithoutOrder = 0;
        
        for (EventRecord event : events) {
            if (event.getId() == null || event.getId().isEmpty()) {
                result.addError("Event missing ID");
            }
            if (event.getMessageType() == null || event.getMessageType().isEmpty()) {
                result.addError("Event missing messageType");
            } else {
                eventTypeCounts.merge(event.getMessageType(), 1, Integer::sum);
            }
            if (event.getCreateTime() == null) {
                result.addError("Event missing createTime: " + event.getId());
            }
            if (event.getPayloadJson() == null || event.getPayloadJson().isEmpty()) {
                eventsWithoutPayload++;
            } else {
                // 验证 payload JSON 格式
                try {
                    JSON.parseObject(event.getPayloadJson());
                } catch (Exception e) {
                    result.addError("Invalid JSON payload in event: " + event.getId());
                }
            }
            if (event.getTaskId() == null || event.getTaskId().isEmpty()) {
                eventsWithoutTaskId++;
            }
            if (event.getMessageOrder() == null && event.getTaskOrder() == null) {
                eventsWithoutOrder++;
            }
        }
        
        result.eventCount = events.size();
        result.eventTypeCounts = eventTypeCounts;
        
        if (eventsWithoutPayload > 0) {
            result.addWarning(eventsWithoutPayload + " events without payload");
        }
        if (eventsWithoutTaskId > 0) {
            result.addWarning(eventsWithoutTaskId + " events without taskId");
        }
        if (eventsWithoutOrder > 0) {
            result.addWarning(eventsWithoutOrder + " events without order information");
        }
    }
    
    /**
     * 验证数据一致性
     */
    private void validateDataConsistency(SessionRecord session, List<MessageRecord> messages, 
                                       List<EventRecord> events, ValidationResult result) {
        // 检查时间一致性
        if (!messages.isEmpty() && !events.isEmpty()) {
            long firstMessageTime = messages.stream()
                    .mapToLong(MessageRecord::getTimestamp)
                    .min().orElse(0);
            long firstEventTime = events.stream()
                    .mapToLong(EventRecord::getCreateTime)
                    .min().orElse(0);
            
            if (Math.abs(firstMessageTime - firstEventTime) > 60000) { // 1分钟差异
                result.addWarning("Large time difference between first message and first event");
            }
        }
        
        // 检查会话状态一致性
        if (session.getStatus() != null) {
            boolean hasResultEvent = events.stream()
                    .anyMatch(e -> "result".equals(e.getMessageType()));
            
            if ("completed".equals(session.getStatus()) && !hasResultEvent) {
                result.addWarning("Session marked as completed but no result event found");
            }
        }
    }
    
    /**
     * 批量验证多个会话
     */
    public Map<String, ValidationResult> validateMultipleSessions(List<String> sessionIds) {
        Map<String, ValidationResult> results = new HashMap<>();
        
        for (String sessionId : sessionIds) {
            try {
                ValidationResult result = validateSession(sessionId);
                results.put(sessionId, result);
            } catch (Exception e) {
                ValidationResult errorResult = new ValidationResult();
                errorResult.sessionId = sessionId;
                errorResult.addError("Validation failed: " + e.getMessage());
                results.put(sessionId, errorResult);
            }
        }
        
        return results;
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        public String sessionId;
        public List<String> errors = new ArrayList<>();
        public List<String> warnings = new ArrayList<>();
        public int messageCount = 0;
        public int eventCount = 0;
        public Map<String, Integer> eventTypeCounts = new HashMap<>();
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
        
        public boolean isValid() {
            return errors.isEmpty();
        }
        
        public boolean hasIssues() {
            return !errors.isEmpty() || !warnings.isEmpty();
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("Session: ").append(sessionId).append("\n");
            sb.append("Messages: ").append(messageCount).append(", Events: ").append(eventCount).append("\n");
            
            if (!errors.isEmpty()) {
                sb.append("Errors:\n");
                for (String error : errors) {
                    sb.append("  - ").append(error).append("\n");
                }
            }
            
            if (!warnings.isEmpty()) {
                sb.append("Warnings:\n");
                for (String warning : warnings) {
                    sb.append("  - ").append(warning).append("\n");
                }
            }
            
            if (!eventTypeCounts.isEmpty()) {
                sb.append("Event types:\n");
                for (Map.Entry<String, Integer> entry : eventTypeCounts.entrySet()) {
                    sb.append("  - ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
                }
            }
            
            return sb.toString();
        }
    }
}
