package com.jd.genie.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 页面快照记录模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageSnapshotRecord {
    
    /** 快照ID */
    private String id;
    
    /** 会话ID */
    private String sessionId;
    
    /** 快照时间戳 */
    private Long timestamp;
    
    /** 快照类型：auto/manual */
    private String type;
    
    /** 是否为最终状态 */
    private Boolean isFinal;
    
    /** 任务状态 */
    private Integer taskStatus;
    
    /** 当前活跃视图：follow/browser/file */
    private String activeView;
    
    /** 快照数据JSON字符串 */
    private String snapshotDataJson;
    
    /** 创建时间 */
    private Long createTime;
    
    /** 快照数据对象（不存储在数据库，用于业务逻辑） */
    private Map<String, Object> snapshotData;
}
