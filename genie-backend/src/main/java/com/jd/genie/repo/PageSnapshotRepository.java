package com.jd.genie.repo;

import com.jd.genie.model.PageSnapshotRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 页面快照数据访问层
 */
@Repository
public class PageSnapshotRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final RowMapper<PageSnapshotRecord> rowMapper = new RowMapper<PageSnapshotRecord>() {
        @Override
        public PageSnapshotRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
            return PageSnapshotRecord.builder()
                    .id(rs.getString("id"))
                    .sessionId(rs.getString("session_id"))
                    .timestamp(rs.getLong("timestamp"))
                    .type(rs.getString("type"))
                    .isFinal(rs.getBoolean("is_final"))
                    .taskStatus(rs.getInt("task_status"))
                    .activeView(rs.getString("active_view"))
                    .snapshotDataJson(rs.getString("snapshot_data_json"))
                    .createTime(rs.getLong("create_time"))
                    .build();
        }
    };

    /**
     * 插入快照记录
     */
    public void insertSnapshot(PageSnapshotRecord record) {
        String sql = "INSERT OR REPLACE INTO page_snapshot(" +
                "id, session_id, timestamp, type, is_final, task_status, active_view, snapshot_data_json, create_time" +
                ") VALUES(?,?,?,?,?,?,?,?,?)";
        
        jdbcTemplate.update(sql,
                record.getId(),
                record.getSessionId(),
                record.getTimestamp(),
                record.getType(),
                record.getIsFinal(),
                record.getTaskStatus(),
                record.getActiveView(),
                record.getSnapshotDataJson(),
                record.getCreateTime()
        );
    }

    /**
     * 根据ID获取快照
     */
    public PageSnapshotRecord getSnapshotById(String snapshotId) {
        String sql = "SELECT * FROM page_snapshot WHERE id = ?";
        
        try {
            return jdbcTemplate.queryForObject(sql, rowMapper, snapshotId);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据会话ID获取快照列表
     */
    public List<PageSnapshotRecord> getSnapshotsBySessionId(String sessionId) {
        String sql = "SELECT * FROM page_snapshot WHERE session_id = ? ORDER BY timestamp DESC";
        return jdbcTemplate.query(sql, rowMapper, sessionId);
    }

    /**
     * 删除快照
     */
    public int deleteSnapshot(String snapshotId) {
        String sql = "DELETE FROM page_snapshot WHERE id = ?";
        return jdbcTemplate.update(sql, snapshotId);
    }

    /**
     * 删除过期快照
     */
    public int deleteExpiredSnapshots(long expireTime) {
        String sql = "DELETE FROM page_snapshot WHERE create_time < ?";
        return jdbcTemplate.update(sql, expireTime);
    }

    /**
     * 获取快照总数
     */
    public int getTotalSnapshotCount() {
        String sql = "SELECT COUNT(*) FROM page_snapshot";
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }

    /**
     * 按类型统计快照数量
     */
    public Map<String, Integer> getSnapshotCountByType() {
        String sql = "SELECT type, COUNT(*) as count FROM page_snapshot GROUP BY type";
        
        Map<String, Integer> result = new HashMap<>();
        jdbcTemplate.query(sql, rs -> {
            result.put(rs.getString("type"), rs.getInt("count"));
        });
        
        return result;
    }

    /**
     * 按会话统计快照数量
     */
    public Map<String, Integer> getSnapshotCountBySession() {
        String sql = "SELECT session_id, COUNT(*) as count FROM page_snapshot GROUP BY session_id ORDER BY count DESC LIMIT 10";
        
        Map<String, Integer> result = new HashMap<>();
        jdbcTemplate.query(sql, rs -> {
            result.put(rs.getString("session_id"), rs.getInt("count"));
        });
        
        return result;
    }

    /**
     * 获取指定时间之后的快照数量
     */
    public int getSnapshotCountSince(long timestamp) {
        String sql = "SELECT COUNT(*) FROM page_snapshot WHERE create_time >= ?";
        return jdbcTemplate.queryForObject(sql, Integer.class, timestamp);
    }

    /**
     * 初始化数据库表
     */
    public void initTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS page_snapshot (
                id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                timestamp INTEGER NOT NULL,
                type TEXT NOT NULL DEFAULT 'auto',
                is_final BOOLEAN NOT NULL DEFAULT 0,
                task_status INTEGER NOT NULL DEFAULT 0,
                active_view TEXT NOT NULL DEFAULT 'follow',
                snapshot_data_json TEXT NOT NULL,
                create_time INTEGER NOT NULL,
                INDEX idx_session_id (session_id),
                INDEX idx_timestamp (timestamp),
                INDEX idx_create_time (create_time)
            )
            """;
        
        jdbcTemplate.execute(sql);
    }
}
