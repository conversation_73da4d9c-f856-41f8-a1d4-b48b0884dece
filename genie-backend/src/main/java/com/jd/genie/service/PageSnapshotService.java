package com.jd.genie.service;

import com.alibaba.fastjson.JSON;
import com.jd.genie.model.PageSnapshotRecord;
import com.jd.genie.repo.PageSnapshotRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 页面快照服务
 * 处理页面状态快照的业务逻辑
 */
@Service
public class PageSnapshotService {

    @Autowired
    private PageSnapshotRepository pageSnapshotRepository;

    /**
     * 保存页面快照
     */
    public String saveSnapshot(Map<String, Object> snapshotData) {
        try {
            // 提取快照基本信息
            String snapshotId = (String) snapshotData.get("id");
            String sessionId = (String) snapshotData.get("sessionId");
            Long timestamp = getLongValue(snapshotData.get("timestamp"));
            String type = (String) snapshotData.get("type");
            Boolean isFinal = getBooleanValue(snapshotData.get("isFinal"));
            Integer taskStatus = getIntegerValue(snapshotData.get("taskStatus"));
            String activeView = (String) snapshotData.get("activeView");

            // 验证必需字段
            if (snapshotId == null || sessionId == null || timestamp == null) {
                throw new IllegalArgumentException("快照ID、会话ID和时间戳不能为空");
            }

            // 创建快照记录
            PageSnapshotRecord record = PageSnapshotRecord.builder()
                    .id(snapshotId)
                    .sessionId(sessionId)
                    .timestamp(timestamp)
                    .type(type != null ? type : "auto")
                    .isFinal(isFinal != null ? isFinal : false)
                    .taskStatus(taskStatus != null ? taskStatus : 0)
                    .activeView(activeView != null ? activeView : "follow")
                    .snapshotDataJson(JSON.toJSONString(snapshotData))
                    .createTime(System.currentTimeMillis())
                    .build();

            // 保存到数据库
            pageSnapshotRepository.insertSnapshot(record);

            return snapshotId;

        } catch (Exception e) {
            throw new RuntimeException("保存快照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取快照
     */
    public PageSnapshotRecord getSnapshot(String snapshotId) {
        try {
            PageSnapshotRecord record = pageSnapshotRepository.getSnapshotById(snapshotId);

            if (record != null && record.getSnapshotDataJson() != null) {
                // 解析JSON数据
                Map<String, Object> snapshotData = JSON.parseObject(record.getSnapshotDataJson(), Map.class);
                record.setSnapshotData(snapshotData);
            }

            return record;

        } catch (Exception e) {
            throw new RuntimeException("获取快照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取会话的快照列表
     */
    public List<Map<String, Object>> getSessionSnapshotList(String sessionId) {
        try {
            List<PageSnapshotRecord> records = pageSnapshotRepository.getSnapshotsBySessionId(sessionId);

            return records.stream()
                    .map(this::convertToListItem)
                    .sorted((a, b) -> Long.compare(
                        getLongValue(b.get("timestamp")),
                        getLongValue(a.get("timestamp"))
                    ))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            throw new RuntimeException("获取快照列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除快照
     */
    public boolean deleteSnapshot(String snapshotId) {
        try {
            return pageSnapshotRepository.deleteSnapshot(snapshotId) > 0;

        } catch (Exception e) {
            throw new RuntimeException("删除快照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量删除快照
     */
    public int batchDeleteSnapshots(List<String> snapshotIds) {
        try {
            int deletedCount = 0;
            for (String snapshotId : snapshotIds) {
                if (pageSnapshotRepository.deleteSnapshot(snapshotId) > 0) {
                    deletedCount++;
                }
            }
            return deletedCount;

        } catch (Exception e) {
            throw new RuntimeException("批量删除快照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理过期快照
     */
    public int cleanupExpiredSnapshots(long maxAge) {
        try {
            long expireTime = System.currentTimeMillis() - maxAge;
            return pageSnapshotRepository.deleteExpiredSnapshots(expireTime);

        } catch (Exception e) {
            throw new RuntimeException("清理过期快照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取快照统计信息
     */
    public Map<String, Object> getSnapshotStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 总快照数
            int totalSnapshots = pageSnapshotRepository.getTotalSnapshotCount();
            stats.put("totalSnapshots", totalSnapshots);

            // 按类型统计
            Map<String, Integer> typeStats = pageSnapshotRepository.getSnapshotCountByType();
            stats.put("typeStats", typeStats);

            // 按会话统计
            Map<String, Integer> sessionStats = pageSnapshotRepository.getSnapshotCountBySession();
            stats.put("sessionStats", sessionStats);

            // 最近7天的快照数
            long sevenDaysAgo = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L;
            int recentSnapshots = pageSnapshotRepository.getSnapshotCountSince(sevenDaysAgo);
            stats.put("recentSnapshots", recentSnapshots);

            return stats;

        } catch (Exception e) {
            throw new RuntimeException("获取统计信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证快照数据完整性
     */
    public Map<String, Object> validateSnapshot(String snapshotId) {
        try {
            PageSnapshotRecord record = getSnapshot(snapshotId);

            if (record == null) {
                return Map.of(
                    "isValid", false,
                    "errors", List.of("快照不存在")
                );
            }

            List<String> errors = new ArrayList<>();
            List<String> warnings = new ArrayList<>();

            Map<String, Object> snapshotData = record.getSnapshotData();

            // 验证基本字段
            if (snapshotData.get("sessionId") == null) {
                errors.add("会话ID缺失");
            }

            if (snapshotData.get("timestamp") == null) {
                errors.add("时间戳缺失");
            }

            // 验证聊天信息
            @SuppressWarnings("unchecked")
            Map<String, Object> chatInfo = (Map<String, Object>) snapshotData.get("chatInfo");
            if (chatInfo != null) {
                if (chatInfo.get("query") == null || chatInfo.get("query").toString().trim().isEmpty()) {
                    warnings.add("用户查询内容为空");
                }
            } else {
                errors.add("聊天信息缺失");
            }

            // 验证页面状态
            @SuppressWarnings("unchecked")
            Map<String, Object> followPage = (Map<String, Object>) snapshotData.get("followPage");
            if (followPage != null) {
                @SuppressWarnings("unchecked")
                List<Object> taskList = (List<Object>) followPage.get("taskList");
                if (taskList == null || taskList.isEmpty()) {
                    warnings.add("任务列表为空");
                }
            } else {
                errors.add("实时跟踪页面状态缺失");
            }

            return Map.of(
                "isValid", errors.isEmpty(),
                "errors", errors,
                "warnings", warnings,
                "snapshotId", snapshotId,
                "validationTime", System.currentTimeMillis()
            );

        } catch (Exception e) {
            return Map.of(
                "isValid", false,
                "errors", List.of("验证过程中发生错误: " + e.getMessage())
            );
        }
    }

    /**
     * 转换为列表项格式
     */
    private Map<String, Object> convertToListItem(PageSnapshotRecord record) {
        Map<String, Object> item = new HashMap<>();

        item.put("id", record.getId());
        item.put("sessionId", record.getSessionId());
        item.put("timestamp", record.getTimestamp());
        item.put("type", record.getType());
        item.put("isFinal", record.getIsFinal());
        item.put("taskStatus", record.getTaskStatus());
        item.put("activeView", record.getActiveView());

        // 生成预览信息
        Map<String, Object> preview = generatePreview(record);
        item.put("preview", preview);

        return item;
    }

    /**
     * 生成快照预览信息
     */
    private Map<String, Object> generatePreview(PageSnapshotRecord record) {
        Map<String, Object> preview = new HashMap<>();

        try {
            if (record.getSnapshotDataJson() != null) {
                Map<String, Object> snapshotData = JSON.parseObject(record.getSnapshotDataJson(), Map.class);

                // 提取查询内容
                @SuppressWarnings("unchecked")
                Map<String, Object> chatInfo = (Map<String, Object>) snapshotData.get("chatInfo");
                if (chatInfo != null && chatInfo.get("query") != null) {
                    String query = chatInfo.get("query").toString();
                    preview.put("query", query.length() > 100 ? query.substring(0, 100) + "..." : query);
                } else {
                    preview.put("query", "未知查询");
                }

                // 统计各类数据
                preview.put("taskCount", getTaskCount(snapshotData));
                preview.put("fileCount", getFileCount(snapshotData));
                preview.put("searchCount", getSearchCount(snapshotData));
            } else {
                preview.put("query", "数据解析失败");
                preview.put("taskCount", 0);
                preview.put("fileCount", 0);
                preview.put("searchCount", 0);
            }
        } catch (Exception e) {
            preview.put("query", "预览生成失败");
            preview.put("taskCount", 0);
            preview.put("fileCount", 0);
            preview.put("searchCount", 0);
        }

        return preview;
    }

    // 辅助方法
    private int getTaskCount(Map<String, Object> snapshotData) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> followPage = (Map<String, Object>) snapshotData.get("followPage");
            if (followPage != null) {
                @SuppressWarnings("unchecked")
                List<Object> taskList = (List<Object>) followPage.get("taskList");
                return taskList != null ? taskList.size() : 0;
            }
        } catch (Exception ignored) {}
        return 0;
    }

    private int getFileCount(Map<String, Object> snapshotData) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> filePage = (Map<String, Object>) snapshotData.get("filePage");
            if (filePage != null) {
                @SuppressWarnings("unchecked")
                List<Object> fileList = (List<Object>) filePage.get("fileList");
                return fileList != null ? fileList.size() : 0;
            }
        } catch (Exception ignored) {}
        return 0;
    }

    private int getSearchCount(Map<String, Object> snapshotData) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> browserPage = (Map<String, Object>) snapshotData.get("browserPage");
            if (browserPage != null) {
                @SuppressWarnings("unchecked")
                List<Object> searchHistory = (List<Object>) browserPage.get("searchHistory");
                return searchHistory != null ? searchHistory.size() : 0;
            }
        } catch (Exception ignored) {}
        return 0;
    }

    private Long getLongValue(Object value) {
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    private Integer getIntegerValue(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Long) return ((Long) value).intValue();
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    private Boolean getBooleanValue(Object value) {
        if (value == null) return null;
        if (value instanceof Boolean) return (Boolean) value;
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }
}
