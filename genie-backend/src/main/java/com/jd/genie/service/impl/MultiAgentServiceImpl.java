package com.jd.genie.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.genie.agent.enums.AgentType;
import com.jd.genie.agent.enums.AutoBotsResultStatus;
import com.jd.genie.agent.enums.ResponseTypeEnum;
import com.jd.genie.config.GenieConfig;
import com.jd.genie.history.service.HistoryService;
import com.jd.genie.handler.AgentResponseHandler;
import com.jd.genie.model.dto.AutoBotsResult;
import com.jd.genie.model.multi.EventResult;
import com.jd.genie.model.req.AgentRequest;
import com.jd.genie.model.req.GptQueryReq;
import com.jd.genie.model.response.AgentResponse;
import com.jd.genie.model.response.GptProcessResult;
import com.jd.genie.service.IMultiAgentService;
import com.jd.genie.util.ChateiUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MultiAgentServiceImpl implements IMultiAgentService {
    @Autowired
    private GenieConfig genieConfig;
    @Autowired
    private Map<AgentType, AgentResponseHandler> handlerMap;
    @Autowired
    private HistoryService historyService;

    @Override
    public AutoBotsResult searchForAgentRequest(GptQueryReq gptQueryReq, SseEmitter sseEmitter) {
        AgentRequest agentRequest = buildAgentRequest(gptQueryReq);
        log.info("{} start handle Agent request: {}", gptQueryReq.getRequestId(), JSON.toJSONString(agentRequest));
        try {
            handleMultiAgentRequest(agentRequest, sseEmitter);
        } catch (Exception e) {
            log.error("{}, error in requestMultiAgent, deepThink: {}, errorMsg: {}", gptQueryReq.getRequestId(), gptQueryReq.getDeepThink(), e.getMessage(), e);
            throw e;
        } finally {
            log.info("{}, agent.query.web.singleRequest end, requestId: {}", gptQueryReq.getRequestId(), JSON.toJSONString(gptQueryReq));
        }

        return ChateiUtils.toAutoBotsResult(agentRequest, AutoBotsResultStatus.loading.name());
    }

    public void handleMultiAgentRequest(AgentRequest autoReq,SseEmitter sseEmitter) {
        long startTime = System.currentTimeMillis();
        Request request = buildHttpRequest(autoReq);
        log.info("{} agentRequest:{}", autoReq.getRequestId(), JSON.toJSONString(request));
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS) // 设置连接超时时间为 60 秒
                .readTimeout(genieConfig.getSseClientReadTimeout(), TimeUnit.SECONDS)    // 设置读取超时时间为 60 秒
                .writeTimeout(1800, TimeUnit.SECONDS)   // 设置写入超时时间为 60 秒
                .callTimeout(genieConfig.getSseClientConnectTimeout(), TimeUnit.SECONDS)    // 设置调用超时时间为 60 秒
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                log.error("onFailure {}", e.getMessage(), e);
                try { historyService.updateSessionStatus(autoReq.getRequestId(), "error", e.getMessage()); } catch (Exception ignored) {}
            }

            @Override
            public void onResponse(Call call, Response response) {
                List<AgentResponse> agentRespList = new ArrayList<>();
                EventResult eventResult = new EventResult();
                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    log.error("{} auto agent empty response body", autoReq.getRequestId());
                    return;
                }

                try {
                    if (!response.isSuccessful()) {
                        log.error("{}, response body is failed: {}", autoReq.getRequestId(), responseBody.string());
                        try { historyService.updateSessionStatus(autoReq.getRequestId(), "error", "http " + response.code()); } catch (Exception ignored) {}
                        return;
                    }

                    String line;
                    BufferedReader reader = new BufferedReader(
                            new InputStreamReader(responseBody.byteStream())
                    );

                    while ((line = reader.readLine()) != null) {
                        if (!line.startsWith("data:")) {
                            continue;
                        }

                        String data = line.substring(5);
                        if (data.equals("[DONE]")) {
                            log.info("{} data equals with [DONE] {}:", autoReq.getRequestId(), data);
                            break;
                        }

                        if (data.startsWith("heartbeat")) {
                            GptProcessResult result = buildHeartbeatData(autoReq.getRequestId());
                            sseEmitter.send(result);
                            log.info("{} heartbeat-data: {}", autoReq.getRequestId(), data);
                            // 可选：记录心跳事件
                            try { historyService.saveEvent(autoReq.getRequestId(), null, "heartbeat", null, null, null, false, null);} catch (Exception ignored) {}
                            continue;
                        }

                        log.info("{} recv from autocontroller: {}", autoReq.getRequestId(), data);
                        AgentResponse agentResponse = JSON.parseObject(data, AgentResponse.class);
                        AgentType agentType = AgentType.fromCode(autoReq.getAgentType());
                        AgentResponseHandler handler = handlerMap.get(agentType);
                        GptProcessResult result = handler.handle(autoReq, agentResponse,agentRespList, eventResult);
                        // 历史：记录 event
                        try {
                            // eventData 携带 messageId/order/task 信息
                            Object payload = buildEventPayload(agentResponse, result);
                            String messageId = agentResponse.getMessageId();
                            String msgType = agentResponse.getMessageType();
                            Integer order = extractMessageOrder(agentResponse, eventResult);
                            String taskId = extractTaskId(agentResponse, result);
                            Integer taskOrder = extractTaskOrder(agentResponse, eventResult);
                            Boolean isFinal = agentResponse.getIsFinal();
                            historyService.saveEvent(autoReq.getRequestId(), messageId, msgType, order, taskId, taskOrder, isFinal, payload);
                            // 文件引用：当结果中有文件信息时
                            if (agentResponse.getResultMap()!=null && agentResponse.getResultMap().get("fileInfo")!=null) {
                                Object fileInfo = agentResponse.getResultMap().get("fileInfo");
                                if (fileInfo instanceof List) {
                                    List<?> list = (List<?>) fileInfo;
                                    for (Object o : list) {
                                        if (o instanceof Map) {
                                            Map<?,?> fm = (Map<?,?>) o;
                                            Object nameObj = fm.get("fileName");
                                            Object ossObj = fm.get("ossUrl");
                                            Object domainObj = fm.get("domainUrl");
                                            String fileName = nameObj == null ? "" : String.valueOf(nameObj);
                                            String downloadUrl = ossObj == null ? "" : String.valueOf(ossObj);
                                            String previewUrl = domainObj == null ? "" : String.valueOf(domainObj);
                                            Long size = null;
                                            Object sizeObj = fm.get("fileSize");
                                            try { size = sizeObj == null ? 0L : Long.valueOf(String.valueOf(sizeObj)); } catch (Exception ignore) {}
                                            String fileType = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf('.')+1) : agentResponse.getMessageType();
                                            historyService.saveFile(autoReq.getRequestId(), fileName, downloadUrl, previewUrl, size, fileType);
                                        }
                                    }
                                }
                            }
                            // 最终结果：写入一条 assistant 消息并更新 session 状态
                            if ("result".equals(agentResponse.getMessageType()) && Boolean.TRUE.equals(agentResponse.getFinish())) {
                                historyService.saveAssistantMessage(autoReq.getRequestId(), agentResponse.getResult(), null, null, null, 2, agentResponse.getResultMap());
                                historyService.updateSessionStatus(autoReq.getRequestId(), "completed", agentResponse.getResult());
                            }
                        } catch (Exception ignored) {}
                        sseEmitter.send(result);
                        if (result.isFinished()) {
                            // 记录任务执行时间
                            log.info("{} task total cost time:{}ms", autoReq.getRequestId(), System.currentTimeMillis() - startTime);
                            sseEmitter.complete();
                        }
                    }
                }catch (Exception e) {
                    log.error("", e);
                }
            }
        });
    }

    private Request buildHttpRequest(AgentRequest autoReq) {
        String reqId = autoReq.getRequestId();
        autoReq.setRequestId(autoReq.getRequestId());
        String url = "http://127.0.0.1:8080/AutoAgent";
        RequestBody body = RequestBody.create(
                MediaType.parse("application/json"),
                JSONObject.toJSONString(autoReq)
        );
        autoReq.setRequestId(reqId);
        return new Request.Builder().url(url).post(body).build();
    }

    private GptProcessResult buildDefaultAutobotsResult(AgentRequest autoReq, String errMsg) {
        GptProcessResult result = new GptProcessResult();
        boolean isRouter = AgentType.ROUTER.getValue().equals(autoReq.getAgentType());
        if (isRouter) {
            result.setStatus("success");
            result.setFinished(true);
            result.setResponse(errMsg);
            result.setTraceId(autoReq.getRequestId());
        } else {
            result.setResultMap(new HashMap<>());
            result.setStatus("failed");
            result.setFinished(true);
            result.setErrorMsg(errMsg);
        }
        return result;
    }

    private AgentRequest buildAgentRequest(GptQueryReq req) {
        AgentRequest request = new AgentRequest();
        request.setRequestId(req.getTraceId());
        request.setErp(req.getUser());
        request.setQuery(req.getQuery());
        request.setAgentType(req.getDeepThink() == 0 ? 5: 3);
        request.setSopPrompt(request.getAgentType() == 3 ? genieConfig.getGenieSopPrompt(): "");
        request.setBasePrompt(request.getAgentType() == 5 ? genieConfig.getGenieBasePrompt() : "");
        request.setIsStream(true);
        request.setOutputStyle(req.getOutputStyle());

        return request;
    }


    private GptProcessResult buildHeartbeatData(String requestId) {
        GptProcessResult result = new GptProcessResult();
        result.setFinished(false);
        result.setStatus("success");
        result.setResponseType(ResponseTypeEnum.text.name());
        result.setResponse("");
        result.setResponseAll("");
        result.setUseTimes(0);
        result.setUseTokens(0);
        result.setReqId(requestId);
        result.setPackageType("heartbeat");
        result.setEncrypted(false);
        return result;
    }

    /**
     * 构建事件载荷数据，确保数据结构完整
     */
    private Object buildEventPayload(AgentResponse agentResponse, GptProcessResult result) {
        Map<String, Object> payload = new HashMap<>();

        // 基础事件数据
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("messageId", agentResponse.getMessageId());
        eventData.put("messageType", agentResponse.getMessageType());
        eventData.put("isFinal", agentResponse.getIsFinal());

        // 添加结果映射数据
        if (agentResponse.getResultMap() != null) {
            eventData.put("resultMap", agentResponse.getResultMap());
        }

        // 添加特定类型的数据
        switch (agentResponse.getMessageType()) {
            case "plan":
                if (agentResponse.getPlan() != null) {
                    eventData.put("plan", agentResponse.getPlan());
                }
                break;
            case "plan_thought":
                if (agentResponse.getPlanThought() != null) {
                    eventData.put("planThought", agentResponse.getPlanThought());
                }
                break;
            case "tool_thought":
                if (agentResponse.getToolThought() != null) {
                    eventData.put("toolThought", agentResponse.getToolThought());
                }
                break;
            case "task":
                if (agentResponse.getTask() != null) {
                    eventData.put("task", agentResponse.getTask());
                }
                break;
            case "result":
                if (agentResponse.getResult() != null) {
                    eventData.put("result", agentResponse.getResult());
                }
                break;
            case "tool_result":
                if (agentResponse.getToolResult() != null) {
                    eventData.put("toolResult", agentResponse.getToolResult());
                }
                break;
            case "task_summary":
                if (agentResponse.getTaskSummary() != null) {
                    eventData.put("taskSummary", agentResponse.getTaskSummary());
                }
                break;
        }

        payload.put("eventData", eventData);
        return payload;
    }

    /**
     * 提取消息顺序
     */
    private Integer extractMessageOrder(AgentResponse agentResponse, EventResult eventResult) {
        // 尝试从eventResult的orderMapping中获取消息顺序
        if (eventResult != null && eventResult.getOrderMapping() != null) {
            String messageType = agentResponse.getMessageType();
            if (messageType != null) {
                return eventResult.getAndIncrOrder(messageType);
            }
        }

        // 尝试从agentResponse的resultMap中获取
        if (agentResponse.getResultMap() != null) {
            Object orderObj = agentResponse.getResultMap().get("messageOrder");
            if (orderObj instanceof Integer) {
                return (Integer) orderObj;
            } else if (orderObj instanceof String) {
                try {
                    return Integer.parseInt((String) orderObj);
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }

        return null;
    }

    /**
     * 提取任务ID
     */
    private String extractTaskId(AgentResponse agentResponse, GptProcessResult result) {
        // 尝试从agentResponse的resultMap中获取
        if (agentResponse.getResultMap() != null) {
            Object taskIdObj = agentResponse.getResultMap().get("taskId");
            if (taskIdObj != null) {
                return String.valueOf(taskIdObj);
            }
        }

        // 尝试从result的resultMap中获取
        if (result.getResultMap() != null) {
            Object taskIdObj = result.getResultMap().get("taskId");
            if (taskIdObj != null) {
                return String.valueOf(taskIdObj);
            }
        }

        // 对于task类型的消息，可以生成一个基于messageId的taskId
        if ("task".equals(agentResponse.getMessageType()) && agentResponse.getMessageId() != null) {
            return "task_" + agentResponse.getMessageId();
        }

        return null;
    }

    /**
     * 提取任务顺序
     */
    private Integer extractTaskOrder(AgentResponse agentResponse, EventResult eventResult) {
        // 尝试从eventResult中获取任务顺序
        if (eventResult != null && eventResult.getTaskOrder() != null) {
            return eventResult.getTaskOrder().get();
        }

        // 尝试从agentResponse的resultMap中获取
        if (agentResponse.getResultMap() != null) {
            Object orderObj = agentResponse.getResultMap().get("taskOrder");
            if (orderObj instanceof Integer) {
                return (Integer) orderObj;
            } else if (orderObj instanceof String) {
                try {
                    return Integer.parseInt((String) orderObj);
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }

        return null;
    }
}
