-- 创建页面快照表
CREATE TABLE IF NOT EXISTS page_snapshot (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    type TEXT NOT NULL DEFAULT 'auto',
    is_final BOOLEAN NOT NULL DEFAULT 0,
    task_status INTEGER NOT NULL DEFAULT 0,
    active_view TEXT NOT NULL DEFAULT 'follow',
    snapshot_data_json TEXT NOT NULL,
    create_time INTEGER NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_page_snapshot_session_id ON page_snapshot(session_id);
CREATE INDEX IF NOT EXISTS idx_page_snapshot_timestamp ON page_snapshot(timestamp);
CREATE INDEX IF NOT EXISTS idx_page_snapshot_create_time ON page_snapshot(create_time);
CREATE INDEX IF NOT EXISTS idx_page_snapshot_type ON page_snapshot(type);
CREATE INDEX IF NOT EXISTS idx_page_snapshot_is_final ON page_snapshot(is_final);
