#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索引擎修复效果
"""
import asyncio
import os
import sys
import time
from typing import List

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from genie_tool.tool.deepsearch import DeepSearch
from genie_tool.tool.search_component.search_engine import SerperSearch

async def test_single_search():
    """测试单个搜索引擎"""
    print("=== 测试单个Serper搜索引擎 ===")
    search_engine = SerperSearch()
    
    start_time = time.time()
    try:
        results = await search_engine.search("python编程教程", "test-request-1")
        end_time = time.time()
        
        print(f"✅ 搜索成功: 找到 {len(results)} 个结果")
        print(f"⏱️ 耗时: {end_time - start_time:.2f}秒")
        
        if results:
            print(f"📄 第一个结果: {results[0].title}")
            print(f"🔗 链接: {results[0].link}")
            
    except Exception as e:
        print(f"❌ 搜索失败: {e}")

async def test_concurrent_search():
    """测试并发搜索"""
    print("\n=== 测试并发搜索 ===")
    search_engine = SerperSearch()
    
    queries = [
        "python机器学习",
        "javascript前端开发", 
        "数据库设计",
        "云计算技术"
    ]
    
    start_time = time.time()
    try:
        # 并发搜索多个查询
        tasks = [search_engine.search_and_dedup(query, f"test-concurrent-{i}") 
                for i, query in enumerate(queries)]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        success_count = 0
        total_docs = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"❌ 查询 '{queries[i]}' 失败: {result}")
            else:
                success_count += 1
                total_docs += len(result)
                print(f"✅ 查询 '{queries[i]}' 成功: {len(result)} 个结果")
        
        print(f"\n📊 总结:")
        print(f"   成功率: {success_count}/{len(queries)} ({success_count/len(queries)*100:.1f}%)")
        print(f"   总文档数: {total_docs}")
        print(f"   总耗时: {end_time - start_time:.2f}秒")
        
    except Exception as e:
        print(f"❌ 并发搜索失败: {e}")

async def test_deep_search():
    """测试深度搜索"""
    print("\n=== 测试深度搜索功能 ===")
    
    deep_search = DeepSearch(engines=["serp"])
    
    start_time = time.time()
    try:
        query = "人工智能在医疗领域的应用"
        print(f"🔍 搜索查询: {query}")
        
        results = []
        async for chunk in deep_search.run(
            query=query,
            request_id="test-deep-search",
            max_loop=1,
            stream=False
        ):
            results.append(chunk)
        
        end_time = time.time()
        
        print(f"✅ 深度搜索完成")
        print(f"⏱️ 耗时: {end_time - start_time:.2f}秒") 
        print(f"📊 返回数据块: {len(results)} 个")
        print(f"📄 找到文档: {len(deep_search.current_docs)} 个")
        
        if deep_search.current_docs:
            print(f"📖 示例文档标题: {deep_search.current_docs[0].title}")
        
    except Exception as e:
        print(f"❌ 深度搜索失败: {e}")

async def main():
    """主测试函数"""
    print("🧪 开始搜索引擎修复效果测试\n")
    
    # 检查环境变量
    print("📋 当前配置:")
    print(f"   SEARCH_TIMEOUT: {os.getenv('SEARCH_TIMEOUT', '未设置')}")
    print(f"   SEARCH_THREAD_NUM: {os.getenv('SEARCH_THREAD_NUM', '未设置')}")
    print(f"   USE_SEARCH_ENGINE: {os.getenv('USE_SEARCH_ENGINE', '未设置')}")
    print(f"   SERPER_SEARCH_URL: {os.getenv('SERPER_SEARCH_URL', '未设置')}")
    print()
    
    # 运行测试
    await test_single_search()
    await test_concurrent_search() 
    await test_deep_search()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️  python-dotenv 未安装，直接使用系统环境变量")
    
    asyncio.run(main())
