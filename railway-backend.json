{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "Dockerfile.backend"}, "deploy": {"startCommand": "java -jar app.jar", "healthcheckPath": "/actuator/health", "healthcheckTimeout": 100, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}, "environments": {"production": {"variables": {"SPRING_PROFILES_ACTIVE": "production", "SERVER_PORT": "8080"}}}}