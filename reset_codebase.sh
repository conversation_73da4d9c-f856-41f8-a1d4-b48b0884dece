#!/bin/bash

# 代码重置脚本
# 用于重置代码库到主分支，保留配置文件

echo "开始代码重置操作..."

# 1. 创建备份目录
BACKUP_DIR="$HOME/genie_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "创建备份目录: $BACKUP_DIR"

# 2. 备份配置文件
echo "备份配置文件..."
if [ -f "genie-backend/src/main/resources/application.yml" ]; then
    cp "genie-backend/src/main/resources/application.yml" "$BACKUP_DIR/"
    echo "✓ 备份 application.yml"
fi

if [ -f "genie-backend/src/main/resources/application-dev.yml" ]; then
    cp "genie-backend/src/main/resources/application-dev.yml" "$BACKUP_DIR/"
    echo "✓ 备份 application-dev.yml"
fi

if [ -f "ui/.env" ]; then
    cp "ui/.env" "$BACKUP_DIR/"
    echo "✓ 备份 .env"
fi

if [ -f "ui/.env.local" ]; then
    cp "ui/.env.local" "$BACKUP_DIR/"
    echo "✓ 备份 .env.local"
fi

# 3. 暂存当前更改
echo "暂存当前更改..."
git add .
git stash push -m "backup_before_reset_$(date +%Y%m%d_%H%M%S)"

# 4. 重置到主分支
echo "重置到主分支..."
git checkout main
git pull origin main

# 5. 恢复配置文件
echo "恢复配置文件..."
if [ -f "$BACKUP_DIR/application.yml" ]; then
    cp "$BACKUP_DIR/application.yml" "genie-backend/src/main/resources/"
    echo "✓ 恢复 application.yml"
fi

if [ -f "$BACKUP_DIR/application-dev.yml" ]; then
    cp "$BACKUP_DIR/application-dev.yml" "genie-backend/src/main/resources/"
    echo "✓ 恢复 application-dev.yml"
fi

if [ -f "$BACKUP_DIR/.env" ]; then
    cp "$BACKUP_DIR/.env" "ui/"
    echo "✓ 恢复 .env"
fi

if [ -f "$BACKUP_DIR/.env.local" ]; then
    cp "$BACKUP_DIR/.env.local" "ui/"
    echo "✓ 恢复 .env.local"
fi

# 6. 清理前端依赖
echo "清理前端依赖..."
cd ui
if [ -d "node_modules" ]; then
    rm -rf node_modules
    echo "✓ 清理 node_modules"
fi

if [ -f "package-lock.json" ]; then
    rm package-lock.json
    echo "✓ 清理 package-lock.json"
fi

# 7. 重新安装依赖
echo "重新安装前端依赖..."
npm install

cd ..

echo "代码重置完成！"
echo "备份文件位置: $BACKUP_DIR"
echo "可以开始实现新的功能了。"
