-- 提取真实的历史数据示例用于前端测试
-- 这个脚本将从SQLite数据库中提取各种类型的事件数据

-- 1. 获取最近的会话信息
SELECT
    'RECENT_SESSIONS' as data_type,
    session_id,
    title,
    status,
    datetime(create_time/1000, 'unixepoch') as create_time_readable
FROM session
ORDER BY create_time DESC
LIMIT 5;

-- 2. 获取各种类型的事件示例
-- 2.1 工具思考事件
SELECT
    'TOOL_THOUGHT_SAMPLE' as data_type,
    message_type,
    task_id,
    message_order,
    task_order,
    is_final,
    datetime(create_time/1000, 'unixepoch') as create_time_readable,
    payload_json
FROM event
WHERE message_type = 'tool_thought'
AND payload_json IS NOT NULL
AND LENGTH(payload_json) > 100
ORDER BY create_time DESC
LIMIT 2;

-- 2.2 深度搜索事件
SELECT
    'DEEP_SEARCH_SAMPLE' as data_type,
    message_type,
    task_id,
    message_order,
    task_order,
    is_final,
    datetime(create_time/1000, 'unixepoch') as create_time_readable,
    CASE
        WHEN LENGTH(payload_json) > 1000 THEN SUBSTR(payload_json, 1, 1000) || '...'
        ELSE payload_json
    END as payload_json_preview
FROM event
WHERE message_type = 'deep_search'
AND payload_json IS NOT NULL
ORDER BY create_time DESC
LIMIT 2;

-- 2.3 结果事件
SELECT
    'RESULT_SAMPLE' as data_type,
    message_type,
    task_id,
    message_order,
    task_order,
    is_final,
    datetime(create_time/1000, 'unixepoch') as create_time_readable,
    payload_json
FROM event
WHERE message_type = 'result'
AND payload_json IS NOT NULL
ORDER BY create_time DESC
LIMIT 2;

-- 2.4 计划思考事件
SELECT
    'PLAN_THOUGHT_SAMPLE' as data_type,
    message_type,
    task_id,
    message_order,
    task_order,
    is_final,
    datetime(create_time/1000, 'unixepoch') as create_time_readable,
    payload_json
FROM event
WHERE message_type = 'plan_thought'
AND payload_json IS NOT NULL
ORDER BY create_time DESC
LIMIT 2;

-- 2.5 任务总结事件
SELECT
    'TASK_SUMMARY_SAMPLE' as data_type,
    message_type,
    task_id,
    message_order,
    task_order,
    is_final,
    datetime(create_time/1000, 'unixepoch') as create_time_readable,
    payload_json
FROM event
WHERE message_type = 'task_summary'
AND payload_json IS NOT NULL
ORDER BY create_time DESC
LIMIT 2;

-- 3. 获取完整的会话事件序列（选择一个最近的会话）
SELECT
    'COMPLETE_SESSION_EVENTS' as data_type,
    e.message_type,
    e.task_id,
    e.message_order,
    e.task_order,
    e.is_final,
    datetime(e.create_time/1000, 'unixepoch') as create_time_readable,
    CASE
        WHEN LENGTH(e.payload_json) > 500 THEN SUBSTR(e.payload_json, 1, 500) || '...'
        ELSE e.payload_json
    END as payload_json_preview
FROM event e
WHERE e.session_id = (
    SELECT session_id
    FROM session
    ORDER BY create_time DESC
    LIMIT 1
)
ORDER BY e.create_time ASC
LIMIT 20;

-- 4. 统计各种事件类型的数据质量
SELECT
    'EVENT_QUALITY_STATS' as data_type,
    message_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN payload_json IS NOT NULL AND payload_json != '' THEN 1 END) as with_payload,
    COUNT(CASE WHEN task_id IS NOT NULL THEN 1 END) as with_task_id,
    COUNT(CASE WHEN message_order IS NOT NULL THEN 1 END) as with_message_order,
    AVG(LENGTH(payload_json)) as avg_payload_length,
    MIN(LENGTH(payload_json)) as min_payload_length,
    MAX(LENGTH(payload_json)) as max_payload_length
FROM event
WHERE payload_json IS NOT NULL
GROUP BY message_type
ORDER BY total_count DESC;

-- 5. 检查payload_json中的数据结构模式
SELECT
    'PAYLOAD_STRUCTURE_ANALYSIS' as data_type,
    message_type,
    COUNT(*) as count,
    COUNT(CASE WHEN payload_json LIKE '%"eventData"%' THEN 1 END) as has_eventData,
    COUNT(CASE WHEN payload_json LIKE '%"resultMap"%' THEN 1 END) as has_resultMap,
    COUNT(CASE WHEN payload_json LIKE '%"toolThought"%' THEN 1 END) as has_toolThought,
    COUNT(CASE WHEN payload_json LIKE '%"searchResult"%' THEN 1 END) as has_searchResult,
    COUNT(CASE WHEN payload_json LIKE '%"taskSummary"%' THEN 1 END) as has_taskSummary,
    COUNT(CASE WHEN payload_json LIKE '%"fileList"%' THEN 1 END) as has_fileList
FROM event
WHERE payload_json IS NOT NULL
GROUP BY message_type
ORDER BY count DESC;

-- 6. 提取特定会话的消息数据
SELECT
    'SESSION_MESSAGES' as data_type,
    m.role,
    CASE
        WHEN LENGTH(m.content) > 200 THEN SUBSTR(m.content, 1, 200) || '...'
        ELSE m.content
    END as content_preview,
    datetime(m.timestamp/1000, 'unixepoch') as timestamp_readable,
    m.result_map_json
FROM message m
WHERE m.session_id = (
    SELECT session_id
    FROM session
    ORDER BY create_time DESC
    LIMIT 1
)
ORDER BY m.timestamp ASC
LIMIT 10;

-- 7. 提取文件引用数据
SELECT
    'FILE_REFERENCES' as data_type,
    f.file_name,
    f.file_type,
    f.size,
    datetime(f.create_time/1000, 'unixepoch') as create_time_readable,
    f.download_url
FROM file_ref f
WHERE f.session_id = (
    SELECT session_id
    FROM session
    ORDER BY create_time DESC
    LIMIT 1
)
ORDER BY f.create_time ASC
LIMIT 5;
