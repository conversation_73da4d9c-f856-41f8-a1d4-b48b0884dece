// 历史数据解析测试脚本
// 用于验证前端历史回放组件的数据解析逻辑

// 模拟真实的SQLite数据库中的事件数据
const sampleEvents = [
  {
    id: "1",
    sessionId: "session-123",
    messageType: "tool_thought",
    messageOrder: 1,
    taskId: "task-456",
    taskOrder: 1,
    isFinal: false,
    payloadJson: `{
      "multiAgent": {},
      "agentType": "5",
      "eventData": {
        "messageOrder": 1,
        "messageType": "task",
        "resultMap": {
          "messageTime": "1754644536140",
          "messageType": "tool_thought",
          "resultMap": {
            "agentType": 5
          },
          "requestId": "genienull:null",
          "messageId": "4b32a486-4462-43e8-8a42-97c8ea0c12f8",
          "finish": false,
          "isFinal": false,
          "toolThought": "正在分析用户需求，准备制定执行计划..."
        },
        "messageId": "4b32a486-4462-43e8-8a42-97c8ea0c12f8",
        "taskId": "1d3f4749-f783-4807-82c8-349fd6923504",
        "taskOrder": 1
      }
    }`,
    createTime: 1754644536140
  },
  {
    id: "2",
    sessionId: "session-123",
    messageType: "deep_search",
    messageOrder: 2,
    taskId: "task-456",
    taskOrder: 2,
    isFinal: false,
    payloadJson: `{
      "multiAgent": {},
      "agentType": "5",
      "eventData": {
        "messageOrder": 2,
        "messageType": "task",
        "resultMap": {
          "messageTime": "1754662948118",
          "messageType": "deep_search",
          "resultMap": {
            "searchResult": {
              "docs": [
                [
                  {
                    "link": "https://news.sina.com.cn/zx/ds/2025-08-08/doc-infkhmtt8929191.shtml",
                    "doc_type": "web_page",
                    "title": "科技晚报AI速递：今日科技热点一览丨2025年8月8日",
                    "content": "科技日新月异，全球创新不断刷新边界。我们为您汇总今日的科技领域最新动向，带您快速了解前沿技术、突破性研究及行业趋势。"
                  }
                ],
                []
              ],
              "query": [
                "2025年8月8日国内重大新闻事件",
                "2025年8月8日国际重大新闻要闻",
                "2025年8月8日全球经济最新动态与数据"
              ]
            },
            "agentType": 5,
            "messageType": "extend",
            "requestId": "geniesession-1754662911558-5723:1754662911569-5125:khkzj",
            "query": "2025年8月8日 国内国际新闻 今日新闻 2025-08-08",
            "isFinal": false,
            "searchFinish": true
          }
        },
        "messageId": "search-message-id",
        "taskId": "1d3f4749-f783-4807-82c8-349fd6923504",
        "taskOrder": 2
      }
    }`,
    createTime: 1754662948118
  },
  {
    id: "3",
    sessionId: "session-123",
    messageType: "result",
    messageOrder: 3,
    taskId: "task-456",
    taskOrder: 3,
    isFinal: true,
    payloadJson: `{
      "multiAgent": {},
      "agentType": "3",
      "eventData": {
        "messageOrder": 3,
        "messageType": "task",
        "resultMap": {
          "result": "已成功整理2025年8月8日的国内外重大新闻事件，包括科技、经济、政治等各个领域的重要资讯。",
          "messageTime": "1754647268584",
          "messageType": "result",
          "resultMap": {
            "agentType": 3,
            "taskSummary": "新闻整理任务已完成，共收集整理了15条重要新闻，涵盖科技创新、经济动态、国际关系等多个领域。",
            "fileList": [
              {
                "fileName": "2025-08-08-news-summary.md",
                "fileSize": 2048,
                "description": "2025年8月8日重大新闻事件汇总，包含详细的新闻内容和分析"
              }
            ]
          },
          "requestId": "geniesession-1754646594146-6197:1754646594158-3401",
          "messageId": "result-message-id",
          "finish": true,
          "isFinal": true
        },
        "messageId": "result-message-id",
        "taskId": "12ec127e-92f0-4e9b-a9ac-017a553ad4f0",
        "taskOrder": 3
      }
    }`,
    createTime: 1754647268584
  }
];

// 测试数据解析函数
function testEventParsing() {
  console.log('=== 历史数据解析测试 ===\n');
  
  sampleEvents.forEach((event, index) => {
    console.log(`测试事件 ${index + 1}: ${event.messageType}`);
    console.log('-------------------');
    
    try {
      const payload = JSON.parse(event.payloadJson);
      const eventData = payload.eventData;
      const resultMap = eventData?.resultMap;
      
      console.log('✅ JSON解析成功');
      console.log('事件数据结构:');
      console.log(`  - messageType: ${event.messageType}`);
      console.log(`  - messageOrder: ${event.messageOrder}`);
      console.log(`  - taskId: ${event.taskId}`);
      console.log(`  - taskOrder: ${event.taskOrder}`);
      console.log(`  - isFinal: ${event.isFinal}`);
      
      // 根据事件类型提取关键信息
      switch (event.messageType) {
        case 'tool_thought':
          const toolThought = resultMap?.toolThought;
          console.log(`  - 工具思考内容: ${toolThought || '无内容'}`);
          break;
          
        case 'deep_search':
          const searchResult = resultMap?.resultMap?.searchResult;
          if (searchResult?.query) {
            console.log(`  - 搜索查询数量: ${searchResult.query.length}`);
            console.log(`  - 搜索查询: ${searchResult.query.slice(0, 2).join(', ')}...`);
          }
          if (searchResult?.docs) {
            const totalDocs = searchResult.docs.flat().length;
            console.log(`  - 搜索结果文档数: ${totalDocs}`);
          }
          console.log(`  - 搜索完成状态: ${resultMap?.resultMap?.searchFinish ? '已完成' : '进行中'}`);
          break;
          
        case 'result':
          const result = resultMap?.result;
          const taskSummary = resultMap?.resultMap?.taskSummary;
          const fileList = resultMap?.resultMap?.fileList;
          console.log(`  - 执行结果: ${result || '无结果'}`);
          console.log(`  - 任务总结: ${taskSummary || '无总结'}`);
          if (fileList && fileList.length > 0) {
            console.log(`  - 生成文件数: ${fileList.length}`);
            fileList.forEach((file, i) => {
              console.log(`    ${i + 1}. ${file.fileName} (${file.fileSize} 字节)`);
            });
          }
          break;
      }
      
    } catch (error) {
      console.log('❌ JSON解析失败:', error.message);
    }
    
    console.log('\n');
  });
}

// 测试事件内容提取函数
function getEventContent(event) {
  try {
    const payload = JSON.parse(event.payloadJson);
    const eventData = payload.eventData;
    const resultMap = eventData?.resultMap;
    
    switch (event.messageType) {
      case 'tool_thought':
        return resultMap?.toolThought || eventData?.toolThought || '工具调用思考';
      case 'deep_search':
        const searchResult = resultMap?.resultMap?.searchResult;
        if (searchResult?.query && Array.isArray(searchResult.query)) {
          return `搜索查询: ${searchResult.query.slice(0, 2).join(', ')}${searchResult.query.length > 2 ? '...' : ''}`;
        }
        return '执行深度搜索';
      case 'result':
        return resultMap?.result || resultMap?.resultMap?.taskSummary || '获得执行结果';
      default:
        return resultMap?.content || eventData?.content || '处理中...';
    }
  } catch (error) {
    return '数据解析错误';
  }
}

// 测试内容提取
function testContentExtraction() {
  console.log('=== 事件内容提取测试 ===\n');
  
  sampleEvents.forEach((event, index) => {
    const content = getEventContent(event);
    console.log(`事件 ${index + 1} (${event.messageType}): ${content}`);
  });
  
  console.log('\n');
}

// 运行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.testHistoryParsing = testEventParsing;
  window.testContentExtraction = testContentExtraction;
  window.sampleEvents = sampleEvents;
  
  console.log('历史数据解析测试已加载到浏览器环境');
  console.log('使用以下命令运行测试:');
  console.log('- testHistoryParsing() - 完整解析测试');
  console.log('- testContentExtraction() - 内容提取测试');
} else {
  // Node.js环境
  testEventParsing();
  testContentExtraction();
}

// 导出测试数据和函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    sampleEvents,
    testEventParsing,
    testContentExtraction,
    getEventContent
  };
}
