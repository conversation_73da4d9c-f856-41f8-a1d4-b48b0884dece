-- 历史数据验证SQL脚本
-- 用于检查SQLite数据库中历史数据的完整性和一致性

-- 1. 检查表结构
.schema session
.schema message  
.schema event
.schema file_ref

-- 2. 统计各表的数据量
SELECT 'session' as table_name, COUNT(*) as count FROM session
UNION ALL
SELECT 'message' as table_name, COUNT(*) as count FROM message
UNION ALL  
SELECT 'event' as table_name, COUNT(*) as count FROM event
UNION ALL
SELECT 'file_ref' as table_name, COUNT(*) as count FROM file_ref;

-- 3. 检查最近的会话数据
SELECT 
    session_id,
    title,
    status,
    create_time,
    update_time,
    datetime(create_time/1000, 'unixepoch') as create_time_readable,
    datetime(update_time/1000, 'unixepoch') as update_time_readable
FROM session 
ORDER BY create_time DESC 
LIMIT 10;

-- 4. 检查消息数据完整性
SELECT 
    s.session_id,
    s.title,
    COUNT(m.id) as message_count,
    COUNT(CASE WHEN m.role = 'user' THEN 1 END) as user_messages,
    COUNT(CASE WHEN m.role = 'assistant' THEN 1 END) as assistant_messages
FROM session s
LEFT JOIN message m ON s.session_id = m.session_id
GROUP BY s.session_id, s.title
ORDER BY s.create_time DESC
LIMIT 10;

-- 5. 检查事件数据完整性
SELECT 
    s.session_id,
    s.title,
    COUNT(e.id) as event_count,
    COUNT(CASE WHEN e.message_type = 'plan' THEN 1 END) as plan_events,
    COUNT(CASE WHEN e.message_type = 'task' THEN 1 END) as task_events,
    COUNT(CASE WHEN e.message_type = 'tool_thought' THEN 1 END) as tool_thought_events,
    COUNT(CASE WHEN e.message_type = 'deep_search' THEN 1 END) as deep_search_events,
    COUNT(CASE WHEN e.message_type = 'result' THEN 1 END) as result_events,
    COUNT(CASE WHEN e.task_id IS NOT NULL THEN 1 END) as events_with_task_id,
    COUNT(CASE WHEN e.message_order IS NOT NULL THEN 1 END) as events_with_order
FROM session s
LEFT JOIN event e ON s.session_id = e.session_id
GROUP BY s.session_id, s.title
ORDER BY s.create_time DESC
LIMIT 10;

-- 6. 检查文件数据完整性
SELECT 
    s.session_id,
    s.title,
    COUNT(f.id) as file_count,
    SUM(f.file_size) as total_file_size,
    GROUP_CONCAT(DISTINCT f.file_type) as file_types
FROM session s
LEFT JOIN file_ref f ON s.session_id = f.session_id
GROUP BY s.session_id, s.title
ORDER BY s.create_time DESC
LIMIT 10;

-- 7. 检查事件载荷数据质量
SELECT 
    message_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN payload_json IS NOT NULL AND payload_json != '' THEN 1 END) as with_payload,
    COUNT(CASE WHEN task_id IS NOT NULL THEN 1 END) as with_task_id,
    COUNT(CASE WHEN message_order IS NOT NULL THEN 1 END) as with_message_order,
    COUNT(CASE WHEN task_order IS NOT NULL THEN 1 END) as with_task_order
FROM event
GROUP BY message_type
ORDER BY total_count DESC;

-- 8. 检查最近事件的载荷数据示例
SELECT 
    e.session_id,
    e.message_type,
    e.task_id,
    e.message_order,
    e.task_order,
    e.is_final,
    datetime(e.create_time/1000, 'unixepoch') as create_time_readable,
    CASE 
        WHEN LENGTH(e.payload_json) > 200 THEN SUBSTR(e.payload_json, 1, 200) || '...'
        ELSE e.payload_json
    END as payload_preview
FROM event e
ORDER BY e.create_time DESC
LIMIT 20;

-- 9. 检查数据一致性问题
-- 9.1 检查孤立的消息（没有对应会话的消息）
SELECT 'orphaned_messages' as issue_type, COUNT(*) as count
FROM message m
LEFT JOIN session s ON m.session_id = s.session_id
WHERE s.session_id IS NULL;

-- 9.2 检查孤立的事件（没有对应会话的事件）
SELECT 'orphaned_events' as issue_type, COUNT(*) as count
FROM event e
LEFT JOIN session s ON e.session_id = s.session_id
WHERE s.session_id IS NULL;

-- 9.3 检查孤立的文件（没有对应会话的文件）
SELECT 'orphaned_files' as issue_type, COUNT(*) as count
FROM file_ref f
LEFT JOIN session s ON f.session_id = s.session_id
WHERE s.session_id IS NULL;

-- 9.4 检查空载荷的事件
SELECT 'empty_payload_events' as issue_type, COUNT(*) as count
FROM event
WHERE payload_json IS NULL OR payload_json = '' OR payload_json = '{}';

-- 9.5 检查缺少任务ID的任务相关事件
SELECT 'task_events_without_task_id' as issue_type, COUNT(*) as count
FROM event
WHERE message_type IN ('task', 'tool_thought', 'deep_search', 'result') 
AND (task_id IS NULL OR task_id = '');

-- 10. 检查特定会话的详细数据（替换为实际的session_id）
-- SELECT 
--     'session_detail' as data_type,
--     session_id,
--     title,
--     status,
--     datetime(create_time/1000, 'unixepoch') as create_time_readable
-- FROM session 
-- WHERE session_id = 'your_session_id_here';

-- SELECT 
--     'messages' as data_type,
--     id,
--     role,
--     CASE 
--         WHEN LENGTH(content) > 100 THEN SUBSTR(content, 1, 100) || '...'
--         ELSE content
--     END as content_preview,
--     datetime(timestamp/1000, 'unixepoch') as timestamp_readable
-- FROM message 
-- WHERE session_id = 'your_session_id_here'
-- ORDER BY timestamp;

-- SELECT 
--     'events' as data_type,
--     id,
--     message_type,
--     task_id,
--     message_order,
--     task_order,
--     is_final,
--     datetime(create_time/1000, 'unixepoch') as create_time_readable,
--     CASE 
--         WHEN LENGTH(payload_json) > 200 THEN SUBSTR(payload_json, 1, 200) || '...'
--         ELSE payload_json
--     END as payload_preview
-- FROM event 
-- WHERE session_id = 'your_session_id_here'
-- ORDER BY create_time;

-- SELECT 
--     'files' as data_type,
--     id,
--     file_name,
--     file_type,
--     file_size,
--     datetime(create_time/1000, 'unixepoch') as create_time_readable
-- FROM file_ref 
-- WHERE session_id = 'your_session_id_here'
-- ORDER BY create_time;
