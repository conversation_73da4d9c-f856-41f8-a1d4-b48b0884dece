#!/bin/bash

# 带页面快照功能的启动脚本
# 用于启动完整的Genie系统并验证页面快照功能

echo "🚀 启动Genie系统（包含页面快照功能）"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查依赖
echo -e "${BLUE}检查系统依赖...${NC}"

# 检查Java
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    echo -e "${GREEN}✅ Java版本: $JAVA_VERSION${NC}"
else
    echo -e "${RED}❌ Java未安装，请安装JDK 17${NC}"
    exit 1
fi

# 检查Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js版本: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js未安装${NC}"
    exit 1
fi

# 检查Python
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo -e "${GREEN}✅ Python版本: $PYTHON_VERSION${NC}"
else
    echo -e "${RED}❌ Python3未安装${NC}"
    exit 1
fi

echo ""

# 启动后端服务
echo -e "${BLUE}启动后端服务...${NC}"
cd genie-backend

# 检查是否有start.sh脚本
if [ -f "start.sh" ]; then
    echo "使用start.sh启动后端..."
    chmod +x start.sh
    ./start.sh &
    BACKEND_PID=$!
    echo "后端服务PID: $BACKEND_PID"
else
    echo -e "${YELLOW}⚠️  未找到start.sh，尝试其他启动方式...${NC}"
    
    # 尝试使用Maven
    if [ -f "pom.xml" ]; then
        echo "使用Maven启动后端..."
        if command -v mvn &> /dev/null; then
            mvn spring-boot:run &
            BACKEND_PID=$!
        else
            echo -e "${RED}❌ Maven未安装，无法启动后端${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ 无法确定后端启动方式${NC}"
        exit 1
    fi
fi

cd ..

# 等待后端启动
echo "等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 后端服务启动成功${NC}"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ 后端服务启动超时${NC}"
        exit 1
    fi
    
    echo -n "."
    sleep 2
done

echo ""

# 启动工具服务
echo -e "${BLUE}启动工具服务...${NC}"
cd genie-tool

# 检查Python虚拟环境
if [ -d ".venv" ]; then
    echo "激活Python虚拟环境..."
    source .venv/bin/activate
else
    echo -e "${YELLOW}⚠️  未找到虚拟环境，使用系统Python${NC}"
fi

# 启动工具服务
if [ -f "start.sh" ]; then
    chmod +x start.sh
    ./start.sh &
    TOOL_PID=$!
    echo "工具服务PID: $TOOL_PID"
else
    echo "使用Python直接启动..."
    python3 -m genie_tool.main &
    TOOL_PID=$!
fi

cd ..

# 等待工具服务启动
echo "等待工具服务启动..."
for i in {1..20}; do
    if curl -s http://localhost:1601/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 工具服务启动成功${NC}"
        break
    fi
    
    if [ $i -eq 20 ]; then
        echo -e "${YELLOW}⚠️  工具服务启动可能有问题，继续启动前端${NC}"
        break
    fi
    
    echo -n "."
    sleep 1
done

echo ""

# 启动前端服务
echo -e "${BLUE}启动前端服务...${NC}"
cd ui

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
fi

# 启动前端
npm start &
FRONTEND_PID=$!
echo "前端服务PID: $FRONTEND_PID"

cd ..

# 等待前端启动
echo "等待前端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 前端服务启动成功${NC}"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ 前端服务启动超时${NC}"
        exit 1
    fi
    
    echo -n "."
    sleep 2
done

echo ""
echo -e "${GREEN}🎉 所有服务启动完成！${NC}"
echo ""
echo "📋 服务信息："
echo "   - 前端界面: http://localhost:3000"
echo "   - 后端API: http://localhost:8080"
echo "   - 工具服务: http://localhost:1601"
echo ""
echo "🔧 页面快照功能："
echo "   - 自动保存: 任务完成时自动触发"
echo "   - 手动保存: 点击ActionView中的'保存记录'按钮"
echo "   - 历史回放: 访问历史页面查看保存的快照"
echo ""
echo "🧪 测试页面快照功能："
echo "   运行: chmod +x test_page_snapshot.sh && ./test_page_snapshot.sh"
echo ""

# 保存PID到文件
echo "BACKEND_PID=$BACKEND_PID" > .genie_pids
echo "TOOL_PID=$TOOL_PID" >> .genie_pids
echo "FRONTEND_PID=$FRONTEND_PID" >> .genie_pids

echo "💾 进程ID已保存到 .genie_pids 文件"
echo ""
echo "🛑 停止所有服务："
echo "   运行: ./stop_genie.sh"
echo ""

# 等待用户输入或服务异常
echo -e "${BLUE}服务正在运行中...${NC}"
echo "按 Ctrl+C 停止所有服务"

# 创建停止脚本
cat > stop_genie.sh << 'EOF'
#!/bin/bash

echo "🛑 停止Genie服务..."

if [ -f ".genie_pids" ]; then
    source .genie_pids
    
    echo "停止前端服务 (PID: $FRONTEND_PID)..."
    kill $FRONTEND_PID 2>/dev/null
    
    echo "停止工具服务 (PID: $TOOL_PID)..."
    kill $TOOL_PID 2>/dev/null
    
    echo "停止后端服务 (PID: $BACKEND_PID)..."
    kill $BACKEND_PID 2>/dev/null
    
    # 等待进程结束
    sleep 3
    
    # 强制杀死残留进程
    pkill -f "spring-boot:run" 2>/dev/null
    pkill -f "npm start" 2>/dev/null
    pkill -f "genie_tool" 2>/dev/null
    
    rm .genie_pids
    echo "✅ 所有服务已停止"
else
    echo "⚠️  未找到PID文件，尝试通过端口杀死进程..."
    
    # 通过端口杀死进程
    lsof -ti:3000 | xargs kill -9 2>/dev/null
    lsof -ti:8080 | xargs kill -9 2>/dev/null
    lsof -ti:1601 | xargs kill -9 2>/dev/null
    
    echo "✅ 进程清理完成"
fi
EOF

chmod +x stop_genie.sh

# 等待中断信号
trap 'echo ""; echo "收到停止信号，正在关闭服务..."; ./stop_genie.sh; exit 0' INT

# 保持脚本运行
while true; do
    sleep 1
done
