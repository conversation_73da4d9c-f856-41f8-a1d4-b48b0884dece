#!/bin/bash

# 页面快照功能测试脚本
# 用于验证页面快照功能是否正常工作

echo "🧪 页面快照功能测试"
echo "===================="

# 检查后端服务是否运行
echo "1. 检查后端服务..."
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务未运行，请先启动后端服务"
    echo "   启动命令: cd genie-backend && ./start.sh"
    exit 1
fi

# 检查前端服务是否运行
echo "2. 检查前端服务..."
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务未运行，请先启动前端服务"
    echo "   启动命令: cd ui && npm start"
    exit 1
fi

# 测试快照保存API
echo "3. 测试快照保存API..."
SNAPSHOT_DATA='{
  "snapshot": {
    "id": "test-snapshot-001",
    "sessionId": "test-session-001",
    "timestamp": '$(date +%s000)',
    "type": "manual",
    "isFinal": true,
    "taskStatus": 3,
    "activeView": "follow",
    "followPage": {
      "taskList": [
        {
          "id": "task-1",
          "messageType": "task",
          "content": "测试任务"
        }
      ],
      "activeTask": null,
      "currentTaskIndex": 0,
      "sliderValue": 0,
      "taskTimeline": []
    },
    "browserPage": {
      "searchHistory": [],
      "activeSearchId": null
    },
    "filePage": {
      "fileList": [],
      "activeFileId": null
    },
    "chatInfo": {
      "query": "这是一个测试查询",
      "response": "这是一个测试响应",
      "thought": "这是测试思考过程"
    },
    "metadata": {
      "userAgent": "test-agent",
      "screenResolution": "1920x1080",
      "saveReason": "user_manual"
    }
  }
}'

SAVE_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "$SNAPSHOT_DATA" \
  http://localhost:8080/web/api/v1/snapshots)

if echo "$SAVE_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 快照保存API测试通过"
    SNAPSHOT_ID=$(echo "$SAVE_RESPONSE" | grep -o '"snapshotId":"[^"]*"' | cut -d'"' -f4)
    echo "   快照ID: $SNAPSHOT_ID"
else
    echo "❌ 快照保存API测试失败"
    echo "   响应: $SAVE_RESPONSE"
fi

# 测试快照加载API
if [ ! -z "$SNAPSHOT_ID" ]; then
    echo "4. 测试快照加载API..."
    LOAD_RESPONSE=$(curl -s http://localhost:8080/web/api/v1/snapshots/$SNAPSHOT_ID)
    
    if echo "$LOAD_RESPONSE" | grep -q '"code":200'; then
        echo "✅ 快照加载API测试通过"
    else
        echo "❌ 快照加载API测试失败"
        echo "   响应: $LOAD_RESPONSE"
    fi
else
    echo "4. ⏭️  跳过快照加载测试（没有有效的快照ID）"
fi

# 测试快照列表API
echo "5. 测试快照列表API..."
LIST_RESPONSE=$(curl -s http://localhost:8080/web/api/v1/snapshots/session/test-session-001)

if echo "$LIST_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 快照列表API测试通过"
    SNAPSHOT_COUNT=$(echo "$LIST_RESPONSE" | grep -o '"total":[0-9]*' | cut -d':' -f2)
    echo "   快照数量: $SNAPSHOT_COUNT"
else
    echo "❌ 快照列表API测试失败"
    echo "   响应: $LIST_RESPONSE"
fi

# 测试统计API
echo "6. 测试统计API..."
STATS_RESPONSE=$(curl -s http://localhost:8080/web/api/v1/snapshots/stats)

if echo "$STATS_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 统计API测试通过"
else
    echo "❌ 统计API测试失败"
    echo "   响应: $STATS_RESPONSE"
fi

# 清理测试数据
if [ ! -z "$SNAPSHOT_ID" ]; then
    echo "7. 清理测试数据..."
    DELETE_RESPONSE=$(curl -s -X DELETE http://localhost:8080/web/api/v1/snapshots/$SNAPSHOT_ID)
    
    if echo "$DELETE_RESPONSE" | grep -q '"code":200'; then
        echo "✅ 测试数据清理完成"
    else
        echo "⚠️  测试数据清理失败，请手动清理"
        echo "   快照ID: $SNAPSHOT_ID"
    fi
else
    echo "7. ⏭️  跳过数据清理（没有创建测试数据）"
fi

echo ""
echo "🎉 页面快照功能测试完成！"
echo ""
echo "📋 测试结果总结："
echo "   - 后端服务: ✅"
echo "   - 前端服务: ✅"
echo "   - 快照保存API: $([ ! -z "$SNAPSHOT_ID" ] && echo "✅" || echo "❌")"
echo "   - 快照加载API: $([ ! -z "$SNAPSHOT_ID" ] && echo "✅" || echo "⏭️")"
echo "   - 快照列表API: $(echo "$LIST_RESPONSE" | grep -q '"code":200' && echo "✅" || echo "❌")"
echo "   - 统计API: $(echo "$STATS_RESPONSE" | grep -q '"code":200' && echo "✅" || echo "❌")"
echo ""
echo "💡 下一步："
echo "   1. 在浏览器中访问 http://localhost:3000"
echo "   2. 创建一个新的对话任务"
echo "   3. 等待任务完成，观察是否自动保存快照"
echo "   4. 点击ActionView中的'保存记录'按钮测试手动保存"
echo "   5. 访问历史页面验证回放功能"
