import React, { forwardRef, useImperativeHandle, useRef, useEffect } from "react";
import classNames from "classnames";
import Title from "./Title";
import { GetProps, Button, Tooltip } from "antd";
import { SaveOutlined } from "@ant-design/icons";
import Tabs from "../Tabs";
import { useSafeState } from "ahooks";
import { useConstants } from "@/hooks";
import { useActionViewStateMonitor } from "@/hooks/usePageSnapshotCapture";
import FilePreview from "./FilePreview";
import { ActionViewItemEnum } from "@/utils";

import BrowserList from "./BrowserList";
import FileList from "./FileList";
import { PlanView, PlanViewAction } from "../PlanView";
import { PanelItemType } from "../ActionPanel";

type ActionViewRef = PlanViewAction & {
  /**
   * 显示文件
   * @param file 文件
   * @returns
   */
  setFilePreview: (file?: CHAT.TFile) => void;
  /**
   * 改变现实的面板
   */
  changeActionView: (item: ActionViewItemEnum) => void;
  /**
   * 手动保存页面快照
   */
  savePageSnapshot?: () => Promise<string | null>;
  /**
   * 获取当前ActionView状态
   */
  getCurrentState?: () => any;
};

const useActionView = () => {
  const ref = useRef<ActionViewRef>(null);

  return ref;
};

type ActionViewProps = {
  title?: React.ReactNode;
  taskList?: (PanelItemType)[];
  activeTask?: CHAT.Task;
  plan?: CHAT.Plan;
  ref?: React.Ref<ActionViewRef>;
  /** 会话ID，用于快照保存 */
  sessionId?: string;
  /** 聊天数据，用于快照保存 */
  chatItem?: CHAT.ChatItem;
  /** 是否显示保存按钮 */
  showSaveButton?: boolean;
  /** 快照保存回调 */
  onSnapshotSaved?: (snapshotId: string) => void;
} & GetProps<typeof Title>;

const ActionViewComp: GenieType.FC<ActionViewProps> = forwardRef((props, ref) => {
  const {
    className,
    onClose,
    title,
    activeTask,
    taskList,
    plan,
    sessionId,
    chatItem,
    showSaveButton = false,
    onSnapshotSaved
  } = props;

  const [ curFileItem, setCurFileItem ] = useSafeState<CHAT.TFile>();

  const planRef = useRef<PlanViewAction>(null);

  const { defaultActiveActionView, actionViewOptions } = useConstants();

  const [ activeActionView, setActiveActionView ] = useSafeState(defaultActiveActionView);

  // 状态监控Hook
  const { getCurrentState } = useActionViewStateMonitor(
    activeActionView,
    taskList || [],
    activeTask,
    undefined // currentTaskIndex 暂时不传，后续可以从FilePreview组件获取
  );

  // 手动保存快照
  const handleSaveSnapshot = async (): Promise<string | null> => {
    if (!sessionId || !chatItem) {
      console.warn('缺少sessionId或chatItem，无法保存快照');
      return null;
    }

    try {
      const { captureCompletePageSnapshot } = await import('@/utils/pageSnapshotCapture');
      const { pageSnapshotService } = await import('@/services/pageSnapshotService');

      const snapshot = captureCompletePageSnapshot(
        sessionId,
        chatItem,
        getCurrentState(),
        { type: 'manual', saveReason: 'user_manual' }
      );

      const result = await pageSnapshotService.saveSnapshot(snapshot);

      if (result.success) {
        onSnapshotSaved?.(result.snapshotId || snapshot.id);
        return result.snapshotId || snapshot.id;
      }

      return null;
    } catch (error) {
      console.error('保存快照失败:', error);
      return null;
    }
  };

  useImperativeHandle(ref, () => {
    return {
      ...planRef.current!,
      setFilePreview: (file) => {
        setActiveActionView(ActionViewItemEnum.file);
        setCurFileItem(file);
      },
      changeActionView: setActiveActionView,
      savePageSnapshot: handleSaveSnapshot,
      getCurrentState
    };
  });
  return (
    <div className={classNames("p-24 pt-8 pb-24 w-full h-full flex flex-col", className)}>
      <div className="flex items-center justify-between">
        <Title onClose={onClose}>{title || '工作空间'}</Title>
        {showSaveButton && sessionId && chatItem && (
          <Tooltip title="保存当前页面状态">
            <Button
              type="primary"
              size="small"
              icon={<SaveOutlined />}
              onClick={handleSaveSnapshot}
            >
              保存记录
            </Button>
          </Tooltip>
        )}
      </div>
      <Tabs value={activeActionView} onChange={setActiveActionView} options={actionViewOptions} />
      {/* 展示区域 */}
      <div className='mt-12 flex-1 h-0 flex flex-col'>
        <FilePreview taskItem={activeTask} taskList={taskList} className={classNames({ 'hidden': activeActionView !== ActionViewItemEnum.follow })} />
        {activeActionView === ActionViewItemEnum.browser && <BrowserList taskList={taskList}/>}
        {activeActionView === ActionViewItemEnum.file && <FileList
          taskList={taskList}
          activeFile={curFileItem}
          clearActiveFile={() => {
            setCurFileItem(undefined);
          }}
        />}
      </div>
      <PlanView plan={plan} ref={planRef} />
    </div>
  );
});

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
const ActionView: typeof ActionViewComp & {
  useActionView: typeof useActionView
} = ActionViewComp;
ActionView.useActionView = useActionView;

export default ActionView;