/**
 * 历史回放组件 - 实现左右双栏布局的历史记录回放
 * 左侧：任务流程时间线
 * 右侧：详细内容展示
 */

import React, { useState, useMemo } from 'react';
import {
  Card,
  Timeline,
  Typography,
  Tag,
  Button,
  Space,
  Collapse,
  Empty,
  Spin,
  Alert,
  Tooltip,
} from 'antd';
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
  FileTextOutlined,
  SearchOutlined,
  CodeOutlined,
  ToolOutlined,
  BulbOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { formatTime } from '@/utils';
import Dialogue from '@/components/Dialogue';
import { buildReplayChat } from '@/utils/historyReplay';
import { testDataConsistency, generateConsistencyReport } from '@/utils/dataConsistencyTest';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface HistoryReplayProps {
  session: any;
  messages: any[];
  events: any[];
  files: any[];
  loading?: boolean;
}

// 事件类型图标映射
const EVENT_ICONS = {
  plan: <BulbOutlined />,
  plan_thought: <BulbOutlined />,
  task: <ToolOutlined />,
  tool_thought: <BulbOutlined />,
  deep_search: <SearchOutlined />,
  result: <CheckCircleOutlined />,
  code: <CodeOutlined />,
  html: <FileTextOutlined />,
  markdown: <FileTextOutlined />,
  file: <FileTextOutlined />,
  task_summary: <CheckCircleOutlined />,
  work_step: <ToolOutlined />,
  task_result: <CheckCircleOutlined />,
  heartbeat: <ClockCircleOutlined />,
};

// 事件类型颜色映射
const EVENT_COLORS = {
  plan: 'blue',
  plan_thought: 'cyan',
  task: 'orange',
  tool_thought: 'cyan',
  deep_search: 'purple',
  result: 'green',
  code: 'geekblue',
  html: 'volcano',
  markdown: 'magenta',
  file: 'gold',
  task_summary: 'green',
  work_step: 'yellow',
  task_result: 'lime',
  heartbeat: 'gray',
};

// 获取事件显示名称
const getEventDisplayName = (event: any): string => {
  const typeMap: Record<string, string> = {
    plan: '制定计划',
    plan_thought: '计划思考',
    task: '执行任务',
    tool_thought: '工具思考',
    deep_search: '深度搜索',
    result: '执行结果',
    code: '代码生成',
    html: 'HTML生成',
    markdown: '文档生成',
    file: '文件操作',
    task_summary: '任务总结',
    work_step: '工作步骤',
    task_result: '任务结果',
    heartbeat: '心跳检测',
  };

  return typeMap[event.messageType] || event.messageType || '未知事件';
};

// 获取事件详细内容
const getEventContent = (event: any): string => {
  try {
    const payload = event.payload || JSON.parse(event.payloadJson || '{}');

    // 从实际数据结构中提取内容
    const eventData = payload.eventData;
    const resultMap = eventData?.resultMap;

    // 根据事件类型提取关键信息
    switch (event.messageType) {
      case 'plan':
        return eventData?.plan?.title || eventData?.plan?.description || '制定执行计划';
      case 'plan_thought':
        return resultMap?.planThought || eventData?.planThought || '分析和思考计划';
      case 'tool_thought':
        return resultMap?.toolThought || eventData?.toolThought || '工具调用思考';
      case 'task':
        return resultMap?.task || eventData?.task || '执行任务';
      case 'deep_search':
        const searchResult = resultMap?.resultMap?.searchResult;
        const answer = resultMap?.answer;
        const query = resultMap?.query;

        // 优先显示生成的答案摘要
        if (answer && answer.length > 0) {
          return `生成内容: ${answer.substring(0, 100)}${answer.length > 100 ? '...' : ''}`;
        }

        // 其次显示搜索查询
        if (query) {
          return `搜索查询: ${query.substring(0, 50)}${query.length > 50 ? '...' : ''}`;
        }

        // 最后显示查询列表
        if (searchResult?.query && Array.isArray(searchResult.query) && searchResult.query.length > 0) {
          return `搜索查询: ${searchResult.query.slice(0, 2).join(', ')}${searchResult.query.length > 2 ? '...' : ''}`;
        }

        return '执行深度搜索';
      case 'result':
        return resultMap?.result || resultMap?.resultMap?.taskSummary || '获得执行结果';
      case 'task_summary':
        return resultMap?.taskSummary || resultMap?.resultMap?.taskSummary || '任务执行完成';
      case 'work_step':
        return resultMap?.workStep || '执行工作步骤';
      case 'task_result':
        return resultMap?.taskResult || '任务结果';
      default:
        return resultMap?.content || eventData?.content || '处理中...';
    }
  } catch (error) {
    console.warn('Failed to parse event content:', error, event);
    return '数据解析错误';
  }
};

// 渲染事件详细内容
const renderEventDetails = (event: any) => {
  try {
    const payload = event.payload || JSON.parse(event.payloadJson || '{}');
    const eventData = payload.eventData;
    const resultMap = eventData?.resultMap;

    switch (event.messageType) {
      case 'deep_search':
        return renderDeepSearchDetails(resultMap);
      case 'result':
        return renderResultDetails(resultMap);
      case 'tool_thought':
        return renderToolThoughtDetails(resultMap);
      case 'plan_thought':
        return renderPlanThoughtDetails(resultMap);
      case 'plan':
        return renderPlanDetails(eventData);
      case 'task':
        return renderTaskDetails(resultMap);
      case 'task_summary':
        return renderTaskSummaryDetails(resultMap);
      case 'work_step':
        return renderWorkStepDetails(resultMap);
      case 'task_result':
        return renderTaskResultDetails(resultMap);
      default:
        return renderDefaultDetails(resultMap || eventData);
    }
  } catch (error) {
    console.warn('Failed to render event details:', error, event);
    return (
      <div className="bg-red-50 p-3 rounded border border-red-200">
        <Text type="danger">数据解析错误</Text>
      </div>
    );
  }
};

// 渲染深度搜索详情
const renderDeepSearchDetails = (resultMap: any) => {
  const searchResult = resultMap?.resultMap?.searchResult;
  const searchFinish = resultMap?.resultMap?.searchFinish;
  const answer = resultMap?.answer; // 从真实数据中发现的answer字段
  const query = resultMap?.query; // 查询字符串

  return (
    <div className="space-y-4">
      <div className="bg-blue-50 p-4 rounded border border-blue-200">
        <div className="flex items-center mb-3">
          <SearchOutlined className="text-blue-600 mr-2" />
          <Text strong className="text-blue-700">深度搜索</Text>
          <Tag color={searchFinish ? 'success' : 'processing'} className="ml-2">
            {searchFinish ? '搜索完成' : '搜索中'}
          </Tag>
        </div>

        {/* 显示查询内容 */}
        {query && (
          <div className="mb-3">
            <Text strong className="text-sm text-blue-600">搜索查询:</Text>
            <div className="mt-1 bg-white p-2 rounded border text-sm">
              {query}
            </div>
          </div>
        )}

        {/* 显示搜索查询列表 */}
        {searchResult?.query && Array.isArray(searchResult.query) && searchResult.query.length > 0 && (
          <div className="mb-3">
            <Text strong className="text-sm text-blue-600">搜索查询 ({searchResult.query.length} 个):</Text>
            <div className="mt-2 space-y-1">
              {searchResult.query.map((query: string, index: number) => (
                <div key={index} className="bg-white p-2 rounded border text-sm">
                  {index + 1}. {query}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 显示搜索结果文档 */}
        {searchResult?.docs && Array.isArray(searchResult.docs) && (
          <div className="mb-3">
            <Text strong className="text-sm text-blue-600">
              搜索结果: {searchResult.docs.flat().length} 个相关文档
            </Text>
            <div className="mt-2 max-h-64 overflow-y-auto space-y-2">
              {searchResult.docs.map((group: any[], groupIndex: number) => (
                group.length > 0 && (
                  <div key={groupIndex}>
                    <Text className="text-xs text-gray-600">第 {groupIndex + 1} 组 ({group.length} 个):</Text>
                    {group.slice(0, 3).map((doc: any, docIndex: number) => (
                      <div key={docIndex} className="bg-white p-3 rounded border ml-2 mt-1">
                        <div className="font-medium text-sm text-gray-800 mb-1">
                          {doc.title || '无标题'}
                        </div>
                        {doc.link && (
                          <div className="text-xs text-blue-600 mb-1 truncate">
                            {doc.link}
                          </div>
                        )}
                        {doc.content && (
                          <div className="text-xs text-gray-600">
                            {doc.content.length > 150 ? `${doc.content.substring(0, 150)}...` : doc.content}
                          </div>
                        )}
                      </div>
                    ))}
                    {group.length > 3 && (
                      <div className="text-xs text-gray-500 ml-2">... 还有 {group.length - 3} 个文档</div>
                    )}
                  </div>
                )
              ))}
            </div>
          </div>
        )}

        {/* 显示生成的答案内容 */}
        {answer && (
          <div>
            <Text strong className="text-sm text-blue-600">生成内容:</Text>
            <div className="mt-2 bg-white p-3 rounded border max-h-64 overflow-y-auto">
              <div className="text-sm text-gray-700 whitespace-pre-wrap">
                {answer.length > 800 ? `${answer.substring(0, 800)}...` : answer}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// 渲染结果详情
const renderResultDetails = (resultMap: any) => {
  const result = resultMap?.result;
  const taskSummary = resultMap?.resultMap?.taskSummary;
  const fileList = resultMap?.resultMap?.fileList;

  return (
    <div className="space-y-4">
      <div className="bg-green-50 p-4 rounded border border-green-200">
        <div className="flex items-center mb-3">
          <CheckCircleOutlined className="text-green-600 mr-2" />
          <Text strong className="text-green-700">任务完成</Text>
        </div>

        {(result || taskSummary) && (
          <div className="mb-3">
            <Text strong className="text-sm text-green-600">执行结果:</Text>
            <div className="mt-2 bg-white p-3 rounded border">
              <Paragraph className="text-sm mb-0">
                {result || taskSummary}
              </Paragraph>
            </div>
          </div>
        )}

        {fileList && Array.isArray(fileList) && fileList.length > 0 && (
          <div>
            <Text strong className="text-sm text-green-600">
              生成的文件 ({fileList.length} 个):
            </Text>
            <div className="mt-2 space-y-2">
              {fileList.map((file: any, index: number) => (
                <div key={index} className="bg-white p-3 rounded border">
                  <div className="flex items-center justify-between mb-1">
                    <Text strong className="text-sm">{file.fileName}</Text>
                    <Text className="text-xs text-gray-500">{file.fileSize} 字节</Text>
                  </div>
                  {file.description && (
                    <div className="text-xs text-gray-600">
                      {file.description.length > 100 ? `${file.description.substring(0, 100)}...` : file.description}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// 渲染工具思考详情
const renderToolThoughtDetails = (resultMap: any) => {
  const toolThought = resultMap?.toolThought;
  const agentType = resultMap?.resultMap?.agentType;
  const messageType = resultMap?.messageType;
  const isFinal = resultMap?.isFinal;
  const finish = resultMap?.finish;

  return (
    <div className="bg-purple-50 p-4 rounded border border-purple-200">
      <div className="flex items-center mb-3">
        <BulbOutlined className="text-purple-600 mr-2" />
        <Text strong className="text-purple-700">工具思考</Text>
        {agentType && (
          <Tag color="purple" className="ml-2">Agent {agentType}</Tag>
        )}
        {(isFinal || finish) && (
          <Tag color="success" className="ml-2">已完成</Tag>
        )}
      </div>

      {toolThought && toolThought.trim() ? (
        <div className="bg-white p-3 rounded border">
          <Paragraph className="text-sm mb-0">{toolThought}</Paragraph>
        </div>
      ) : (
        <div className="bg-white p-3 rounded border">
          <div className="text-sm text-gray-500 flex items-center">
            <LoadingOutlined className="mr-2" />
            Agent {agentType || '未知'} 正在思考中...
          </div>
          {messageType && (
            <div className="text-xs text-gray-400 mt-1">
              消息类型: {messageType}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// 渲染计划思考详情
const renderPlanThoughtDetails = (resultMap: any) => {
  const planThought = resultMap?.planThought;

  return (
    <div className="bg-cyan-50 p-4 rounded border border-cyan-200">
      <div className="flex items-center mb-3">
        <BulbOutlined className="text-cyan-600 mr-2" />
        <Text strong className="text-cyan-700">计划思考</Text>
      </div>

      {planThought ? (
        <div className="bg-white p-3 rounded border">
          <Paragraph className="text-sm mb-0">{planThought}</Paragraph>
        </div>
      ) : (
        <div className="text-sm text-gray-500">计划分析中...</div>
      )}
    </div>
  );
};

// 渲染计划详情
const renderPlanDetails = (eventData: any) => {
  const plan = eventData?.plan;

  return (
    <div className="bg-blue-50 p-4 rounded border border-blue-200">
      <div className="flex items-center mb-3">
        <BulbOutlined className="text-blue-600 mr-2" />
        <Text strong className="text-blue-700">执行计划</Text>
      </div>

      {plan ? (
        <div className="bg-white p-3 rounded border">
          <pre className="text-sm whitespace-pre-wrap">
            {typeof plan === 'string' ? plan : JSON.stringify(plan, null, 2)}
          </pre>
        </div>
      ) : (
        <div className="text-sm text-gray-500">计划制定中...</div>
      )}
    </div>
  );
};

// 渲染任务详情
const renderTaskDetails = (resultMap: any) => {
  const task = resultMap?.task;

  return (
    <div className="bg-orange-50 p-4 rounded border border-orange-200">
      <div className="flex items-center mb-3">
        <ToolOutlined className="text-orange-600 mr-2" />
        <Text strong className="text-orange-700">执行任务</Text>
      </div>

      {task ? (
        <div className="bg-white p-3 rounded border">
          <Paragraph className="text-sm mb-0">{task}</Paragraph>
        </div>
      ) : (
        <div className="text-sm text-gray-500">任务执行中...</div>
      )}
    </div>
  );
};

// 渲染任务总结详情
const renderTaskSummaryDetails = (resultMap: any) => {
  const taskSummary = resultMap?.taskSummary || resultMap?.resultMap?.taskSummary;

  return (
    <div className="bg-green-50 p-4 rounded border border-green-200">
      <div className="flex items-center mb-3">
        <CheckCircleOutlined className="text-green-600 mr-2" />
        <Text strong className="text-green-700">任务总结</Text>
      </div>

      {taskSummary ? (
        <div className="bg-white p-3 rounded border">
          <Paragraph className="text-sm mb-0">{taskSummary}</Paragraph>
        </div>
      ) : (
        <div className="text-sm text-gray-500">总结生成中...</div>
      )}
    </div>
  );
};

// 渲染工作步骤详情
const renderWorkStepDetails = (resultMap: any) => {
  const workStep = resultMap?.workStep;

  return (
    <div className="bg-yellow-50 p-4 rounded border border-yellow-200">
      <div className="flex items-center mb-3">
        <ToolOutlined className="text-yellow-600 mr-2" />
        <Text strong className="text-yellow-700">工作步骤</Text>
      </div>

      {workStep ? (
        <div className="bg-white p-3 rounded border">
          <Paragraph className="text-sm mb-0">{workStep}</Paragraph>
        </div>
      ) : (
        <div className="text-sm text-gray-500">步骤执行中...</div>
      )}
    </div>
  );
};

// 渲染任务结果详情
const renderTaskResultDetails = (resultMap: any) => {
  const taskResult = resultMap?.taskResult;

  return (
    <div className="bg-indigo-50 p-4 rounded border border-indigo-200">
      <div className="flex items-center mb-3">
        <CheckCircleOutlined className="text-indigo-600 mr-2" />
        <Text strong className="text-indigo-700">任务结果</Text>
      </div>

      {taskResult ? (
        <div className="bg-white p-3 rounded border">
          <Paragraph className="text-sm mb-0">{taskResult}</Paragraph>
        </div>
      ) : (
        <div className="text-sm text-gray-500">结果生成中...</div>
      )}
    </div>
  );
};

// 渲染默认详情
const renderDefaultDetails = (data: any) => {
  return (
    <div className="bg-gray-50 p-4 rounded border border-gray-200">
      <div className="flex items-center mb-3">
        <FileTextOutlined className="text-gray-600 mr-2" />
        <Text strong className="text-gray-700">事件详情</Text>
      </div>

      {data ? (
        <div className="bg-white p-3 rounded border">
          <pre className="text-sm whitespace-pre-wrap overflow-auto max-h-64">
            {typeof data === 'string' ? data : JSON.stringify(data, null, 2)}
          </pre>
        </div>
      ) : (
        <div className="text-sm text-gray-500">暂无详细信息</div>
      )}
    </div>
  );
};

const HistoryReplay: React.FC<HistoryReplayProps> = ({
  session,
  messages,
  events,
  files,
  loading = false,
}) => {
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [activePanel, setActivePanel] = useState<'timeline' | 'dialogue'>('dialogue');
  const [consistencyReport, setConsistencyReport] = useState<string | null>(null);
  const [showConsistencyCheck, setShowConsistencyCheck] = useState(false);

  // 构建回放聊天数据
  const replayChat = useMemo(() => {
    return buildReplayChat(events, messages, session?.sessionId, session);
  }, [events, messages, session]);

  // 按时间排序的事件列表
  const sortedEvents = useMemo(() => {
    return [...events].sort((a, b) => (a.createTime || 0) - (b.createTime || 0));
  }, [events]);

  // 数据一致性检查
  const handleConsistencyCheck = async () => {
    if (!session?.sessionId) return;

    setShowConsistencyCheck(true);
    try {
      const result = await testDataConsistency(session.sessionId);
      const report = generateConsistencyReport(result);
      setConsistencyReport(report);
    } catch (error) {
      setConsistencyReport(`数据一致性检查失败: ${error}`);
    }
  };

  // 时间线数据
  const timelineItems = useMemo(() => {
    return sortedEvents.map((event, index) => ({
      key: event.id || index,
      dot: EVENT_ICONS[event.messageType as keyof typeof EVENT_ICONS] || <ClockCircleOutlined />,
      color: EVENT_COLORS[event.messageType as keyof typeof EVENT_COLORS] || 'gray',
      children: (
        <div
          className="cursor-pointer p-2 rounded hover:bg-gray-50"
          onClick={() => setSelectedEvent(event)}
        >
          <div className="flex items-center justify-between mb-1">
            <Text strong>{getEventDisplayName(event)}</Text>
            <Text type="secondary" className="text-xs">
              {formatTime(event.createTime)}
            </Text>
          </div>
          <div className="text-sm text-gray-600 line-clamp-2">
            {getEventContent(event)}
          </div>
          <div className="mt-1">
            <Tag
              color={EVENT_COLORS[event.messageType as keyof typeof EVENT_COLORS] || 'default'}
              size="small"
            >
              {event.messageType}
            </Tag>
            {event.taskId && (
              <Tag size="small" className="ml-1">
                Task: {event.taskId.substring(0, 8)}
              </Tag>
            )}
          </div>
        </div>
      ),
    }));
  }, [sortedEvents]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!events.length && !messages.length) {
    return (
      <Empty
        description="暂无历史数据"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return (
    <div className="h-full flex">
      {/* 左侧：任务流程时间线 */}
      <div className="w-1/3 border-r border-gray-200 overflow-auto">
        <Card
          size="small"
          title="执行时间线"
          className="h-full"
          styles={{ body: { padding: '16px', height: 'calc(100% - 57px)', overflow: 'auto' } }}
          extra={
            <Space>
              <Button
                size="small"
                type={activePanel === 'timeline' ? 'primary' : 'default'}
                onClick={() => setActivePanel('timeline')}
              >
                时间线
              </Button>
              <Button
                size="small"
                type={activePanel === 'dialogue' ? 'primary' : 'default'}
                onClick={() => setActivePanel('dialogue')}
              >
                对话视图
              </Button>
              <Tooltip title="检查数据一致性">
                <Button
                  size="small"
                  icon={<InfoCircleOutlined />}
                  onClick={handleConsistencyCheck}
                >
                  数据检查
                </Button>
              </Tooltip>
            </Space>
          }
        >
          {/* 数据一致性检查结果 */}
          {showConsistencyCheck && consistencyReport && (
            <Alert
              message="数据一致性检查报告"
              description={
                <pre className="whitespace-pre-wrap text-xs max-h-40 overflow-auto">
                  {consistencyReport}
                </pre>
              }
              type={consistencyReport.includes('失败') || consistencyReport.includes('错误') ? 'warning' : 'info'}
              showIcon
              closable
              onClose={() => setShowConsistencyCheck(false)}
              className="mb-4"
            />
          )}

          {activePanel === 'timeline' ? (
            <Timeline
              mode="left"
              items={timelineItems}
              className="mt-4"
            />
          ) : (
            replayChat && (
              <div className="mt-4">
                <Dialogue
                  chat={replayChat}
                  deepThink={true}
                />
              </div>
            )
          )}
        </Card>
      </div>

      {/* 右侧：详细内容展示 */}
      <div className="flex-1 overflow-auto">
        <Card
          size="small"
          title="详细内容"
          className="h-full"
          styles={{ body: { padding: '16px', height: 'calc(100% - 57px)', overflow: 'auto' } }}
        >
          {selectedEvent ? (
            <div>
              {/* 事件基本信息 */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <Title level={4} className="mb-0">
                    {getEventDisplayName(selectedEvent)}
                  </Title>
                  <Text type="secondary">
                    {formatTime(selectedEvent.createTime)}
                  </Text>
                </div>
                <Space>
                  <Tag color={EVENT_COLORS[selectedEvent.messageType as keyof typeof EVENT_COLORS] || 'default'}>
                    {selectedEvent.messageType}
                  </Tag>
                  {selectedEvent.taskId && (
                    <Tag>任务ID: {selectedEvent.taskId}</Tag>
                  )}
                  {selectedEvent.messageOrder && (
                    <Tag>顺序: {selectedEvent.messageOrder}</Tag>
                  )}
                </Space>
              </div>

              {/* 事件详细内容 */}
              <Collapse defaultActiveKey={['content']} ghost>
                <Panel header="事件内容" key="content">
                  {renderEventDetails(selectedEvent)}
                </Panel>

                <Panel header="原始数据" key="raw">
                  <div className="bg-gray-50 p-3 rounded">
                    <pre className="text-xs overflow-auto max-h-64">
                      {JSON.stringify(selectedEvent.payload || JSON.parse(selectedEvent.payloadJson || '{}'), null, 2)}
                    </pre>
                  </div>
                </Panel>
              </Collapse>
            </div>
          ) : (
            <div className="text-center text-gray-500 mt-8">
              <FileTextOutlined className="text-4xl mb-4" />
              <div>请从左侧时间线选择一个事件查看详细内容</div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default HistoryReplay;
