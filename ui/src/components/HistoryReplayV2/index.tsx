/**
 * 历史回放组件V2
 * 基于"状态快照 + 渲染器复用"的新架构
 * 直接复用ActionView组件实现完全一致的显示效果
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Card, Slider, Button, Space, Tag, Typography, Alert, Empty, Spin } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StepBackwardOutlined, 
  StepForwardOutlined,
  ReloadOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { formatTime } from '@/utils';
import ActionView from '@/components/ActionView';
import Dialogue from '@/components/Dialogue';
import { PAGE_SNAPSHOT } from '@/types/pageSnapshot';
import { pageSnapshotService } from '@/services/pageSnapshotService';

const { Title, Text } = Typography;

interface HistoryReplayV2Props {
  /** 会话ID */
  sessionId: string;
  /** 是否显示对话视图 */
  showDialogue?: boolean;
  /** 类名 */
  className?: string;
}

/**
 * 时间轴控制器组件
 */
const TimelineController: React.FC<{
  snapshots: PAGE_SNAPSHOT.SnapshotListItem[];
  currentIndex: number;
  onChange: (index: number) => void;
  loading?: boolean;
}> = ({ snapshots, currentIndex, onChange, loading }) => {
  const [isPlaying, setIsPlaying] = useState(false);

  // 自动播放功能
  useEffect(() => {
    if (!isPlaying || snapshots.length <= 1) return;

    const interval = setInterval(() => {
      onChange(prev => {
        const nextIndex = prev + 1;
        if (nextIndex >= snapshots.length) {
          setIsPlaying(false);
          return prev;
        }
        return nextIndex;
      });
    }, 2000); // 每2秒切换一次

    return () => clearInterval(interval);
  }, [isPlaying, snapshots.length, onChange]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handlePrevious = () => {
    setIsPlaying(false);
    onChange(Math.max(0, currentIndex - 1));
  };

  const handleNext = () => {
    setIsPlaying(false);
    onChange(Math.min(snapshots.length - 1, currentIndex + 1));
  };

  const handleReset = () => {
    setIsPlaying(false);
    onChange(0);
  };

  if (snapshots.length === 0) {
    return (
      <Card size="small" className="mb-4">
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="没有找到页面快照"
        />
      </Card>
    );
  }

  const currentSnapshot = snapshots[currentIndex];
  const marks = snapshots.reduce((acc, snapshot, index) => {
    acc[index] = {
      style: { fontSize: '12px' },
      label: formatTime(snapshot.timestamp)
    };
    return acc;
  }, {} as Record<number, any>);

  return (
    <Card size="small" className="mb-4">
      <div className="space-y-4">
        {/* 当前快照信息 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <HistoryOutlined className="text-blue-500" />
            <Text strong>历史回放</Text>
            <Tag color={currentSnapshot?.type === 'manual' ? 'blue' : 'green'}>
              {currentSnapshot?.type === 'manual' ? '手动保存' : '自动保存'}
            </Tag>
            {currentSnapshot?.isFinal && (
              <Tag color="success">最终状态</Tag>
            )}
          </div>
          <Text type="secondary" className="text-sm">
            {currentIndex + 1} / {snapshots.length}
          </Text>
        </div>

        {/* 时间轴滑块 */}
        <div className="px-2">
          <Slider
            min={0}
            max={snapshots.length - 1}
            value={currentIndex}
            onChange={onChange}
            marks={marks}
            step={1}
            disabled={loading}
            tooltip={{
              formatter: (value) => {
                const snapshot = snapshots[value || 0];
                return snapshot ? formatTime(snapshot.timestamp) : '';
              }
            }}
          />
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center justify-center space-x-2">
          <Button
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleReset}
            disabled={loading || currentIndex === 0}
            title="重置到开始"
          />
          <Button
            size="small"
            icon={<StepBackwardOutlined />}
            onClick={handlePrevious}
            disabled={loading || currentIndex === 0}
            title="上一步"
          />
          <Button
            size="small"
            type={isPlaying ? 'primary' : 'default'}
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={handlePlayPause}
            disabled={loading || snapshots.length <= 1}
            title={isPlaying ? '暂停播放' : '自动播放'}
          >
            {isPlaying ? '暂停' : '播放'}
          </Button>
          <Button
            size="small"
            icon={<StepForwardOutlined />}
            onClick={handleNext}
            disabled={loading || currentIndex === snapshots.length - 1}
            title="下一步"
          />
        </div>

        {/* 快照预览信息 */}
        {currentSnapshot && (
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-sm space-y-1">
              <div><Text strong>查询:</Text> {currentSnapshot.preview.query}</div>
              <div className="flex space-x-4">
                <span><Text strong>任务:</Text> {currentSnapshot.preview.taskCount}</span>
                <span><Text strong>文件:</Text> {currentSnapshot.preview.fileCount}</span>
                <span><Text strong>搜索:</Text> {currentSnapshot.preview.searchCount}</span>
              </div>
              <div><Text strong>活跃页面:</Text> 
                <Tag size="small" className="ml-1">
                  {currentSnapshot.activeView === 'follow' ? '实时跟踪' : 
                   currentSnapshot.activeView === 'browser' ? '浏览器' : '文件'}
                </Tag>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

/**
 * 历史回放主组件
 */
const HistoryReplayV2: React.FC<HistoryReplayV2Props> = ({
  sessionId,
  showDialogue = false,
  className
}) => {
  const [snapshots, setSnapshots] = useState<PAGE_SNAPSHOT.SnapshotListItem[]>([]);
  const [currentSnapshotIndex, setCurrentSnapshotIndex] = useState(0);
  const [currentSnapshot, setCurrentSnapshot] = useState<PAGE_SNAPSHOT.CompletePageSnapshot | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载快照列表
  useEffect(() => {
    const loadSnapshots = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const snapshotList = await pageSnapshotService.getSessionSnapshots(sessionId);
        
        if (snapshotList.length === 0) {
          setError('该会话没有保存的页面快照');
          return;
        }
        
        setSnapshots(snapshotList);
        setCurrentSnapshotIndex(snapshotList.length - 1); // 默认显示最新的快照
        
      } catch (err) {
        setError(`加载快照失败: ${err}`);
      } finally {
        setLoading(false);
      }
    };

    if (sessionId) {
      loadSnapshots();
    }
  }, [sessionId]);

  // 加载当前快照详细数据
  useEffect(() => {
    const loadCurrentSnapshot = async () => {
      if (snapshots.length === 0) return;
      
      try {
        const snapshotId = snapshots[currentSnapshotIndex]?.id;
        if (!snapshotId) return;
        
        const snapshot = await pageSnapshotService.loadSnapshot(snapshotId);
        setCurrentSnapshot(snapshot);
        
      } catch (err) {
        console.error('加载快照详情失败:', err);
        setError(`加载快照详情失败: ${err}`);
      }
    };

    loadCurrentSnapshot();
  }, [snapshots, currentSnapshotIndex]);

  // 构建聊天数据用于Dialogue组件
  const chatItemForDialogue = useMemo(() => {
    if (!currentSnapshot) return null;

    return {
      query: currentSnapshot.chatInfo.query,
      response: currentSnapshot.chatInfo.response,
      sessionId: currentSnapshot.sessionId,
      requestId: `replay-${currentSnapshot.id}`,
      loading: false,
      forceStop: false,
      tasks: currentSnapshot.followPage.taskList,
      thought: currentSnapshot.chatInfo.thought,
      taskStatus: currentSnapshot.taskStatus,
      plan: currentSnapshot.chatInfo.plan,
      multiAgent: {
        tasks: [currentSnapshot.followPage.taskList],
        plan: currentSnapshot.chatInfo.plan
      }
    } as CHAT.ChatItem;
  }, [currentSnapshot]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" tip="加载历史快照中..." />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        type="error"
        message="加载失败"
        description={error}
        showIcon
        action={
          <Button size="small" onClick={() => window.location.reload()}>
            重新加载
          </Button>
        }
      />
    );
  }

  return (
    <div className={`h-full flex flex-col ${className || ''}`}>
      {/* 时间轴控制器 */}
      <TimelineController
        snapshots={snapshots}
        currentIndex={currentSnapshotIndex}
        onChange={setCurrentSnapshotIndex}
        loading={loading}
      />

      {/* 内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：对话视图（可选） */}
        {showDialogue && chatItemForDialogue && (
          <div className="w-1/2 border-r border-gray-200 overflow-auto">
            <Card size="small" title="对话回放" className="h-full">
              <Dialogue
                chat={chatItemForDialogue}
                deepThink={false}
                changeTask={() => {}} // 历史回放模式下禁用交互
                changeFile={() => {}}
                changePlan={() => {}}
              />
            </Card>
          </div>
        )}

        {/* 右侧：ActionView回放 */}
        <div className={`${showDialogue ? 'w-1/2' : 'w-full'} overflow-hidden`}>
          {currentSnapshot ? (
            <ActionView
              title={`页面回放 - ${formatTime(currentSnapshot.timestamp)}`}
              taskList={currentSnapshot.followPage.taskList}
              activeTask={currentSnapshot.followPage.activeTask}
              plan={currentSnapshot.chatInfo.plan}
              // 历史回放模式下禁用保存功能
              showSaveButton={false}
              onClose={() => {}}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <Empty description="快照数据加载中..." />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HistoryReplayV2;
