// 历史记录管理hooks
// 新增功能：历史记录的React状态管理

import { useState, useEffect, useCallback } from 'react';
import {
  getSessionHistory,
  saveSessionHistory,
  getSessionMessages,
  saveMessageHistory,
  getSessionFiles,
  saveFileHistory,
  deleteSession,
  searchSessions,
  getHistoryStats,
  cleanExpiredData,
  exportHistoryData,
  convertChatItemToHistory,
} from '@/utils/history';
import { historyApi } from '@/services/history';
import {
  normalizeSessionData,
  normalizeMessageData,
  normalizeFileData,
  normalizeEventData,
  normalizeMessagesArray,
  normalizeSessionsArray,
  normalizeFilesArray,
  validateMessageData,
  hasProcessData,
} from '@/utils/dataTransform';

/**
 * 历史记录列表管理hook
 */
export function useHistoryList() {
  const [sessions, setSessions] = useState<HISTORY.SessionHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<HISTORY.SearchFilter>({});
  const [pagination, setPagination] = useState<HISTORY.PaginationParams>({
    page: 1,
    pageSize: 20,
  });

  // 加载会话列表
  const loadSessions = useCallback(async () => {
    setLoading(true);
    try {
      // 优先从后端获取
      let allSessions: HISTORY.SessionHistory[] | null = null;
      try {
        const serverSessions = await historyApi.listSessions({
          keyword: filter.keyword,
          status: filter.status?.join(','),
          start: filter.dateRange?.[0],
          end: filter.dateRange?.[1],
          tags: filter.tags?.join(','),
          sortBy: filter.sortBy,
          sortOrder: filter.sortOrder?.toUpperCase() as any,
          page: pagination.page,
          pageSize: pagination.pageSize,
        });
        // 使用统一的数据转换工具
        allSessions = normalizeSessionsArray(serverSessions || []);
      } catch (e) {
        // 网络失败回退本地
        allSessions = null;
      }
      if (!allSessions) {
        allSessions = searchSessions(filter);
      }
      const startIndex = (pagination.page - 1) * pagination.pageSize;
      const endIndex = startIndex + pagination.pageSize;
      const paginatedSessions = allSessions.slice(startIndex, endIndex);

      // 先渲染基本列表
      setSessions(paginatedSessions);
      setPagination(prev => ({ ...prev, total: allSessions.length }));

      // 异步补齐消息计数（从后端详情或本地兜底）
      try {
        const enriched = await Promise.all(
          paginatedSessions.map(async (s) => {
            // 若已有有效 messageCount 则跳过
            if (typeof s.messageCount === 'number' && s.messageCount > 0) {
              return s;
            }
            try {
              const detail = await historyApi.getSessionDetail(s.sessionId);
              const serverCount = Array.isArray(detail?.messages) ? detail.messages.length : 0;
              if (serverCount > 0) {
                return { ...s, messageCount: serverCount };
              }
            } catch {}
            // 本地兜底
            try {
              const localCount = getSessionMessages(s.sessionId).length;
              return { ...s, messageCount: localCount };
            } catch {
              return s;
            }
          })
        );
        setSessions(enriched);
      } catch {
        // 忽略补齐阶段错误
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    } finally {
      setLoading(false);
    }
  }, [filter, pagination.page, pagination.pageSize]);

  // 删除会话
  const handleDeleteSession = useCallback(async (sessionId: string) => {
    try {
      deleteSession(sessionId);
      await loadSessions();
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  }, [loadSessions]);

  // 更新筛选条件
  const updateFilter = useCallback((newFilter: Partial<HISTORY.SearchFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
  }, []);

  // 更新分页
  const updatePagination = useCallback((newPagination: Partial<HISTORY.PaginationParams>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  // 清理过期数据
  const cleanExpired = useCallback(async () => {
    try {
      cleanExpiredData();
      await loadSessions();
    } catch (error) {
      console.error('Failed to clean expired data:', error);
    }
  }, [loadSessions]);

  // 导出数据
  const exportData = useCallback(() => {
    try {
      const data = exportHistoryData();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `genie-history-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export data:', error);
    }
  }, []);

  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  return {
    sessions,
    loading,
    filter,
    pagination,
    updateFilter,
    updatePagination,
    deleteSession: handleDeleteSession,
    refreshSessions: loadSessions,
    cleanExpired,
    exportData,
  };
}

/**
 * 会话详情管理hook
 */
export function useSessionDetail(sessionId: string) {
  const [session, setSession] = useState<HISTORY.SessionHistory | null>(null);
  const [messages, setMessages] = useState<HISTORY.MessageHistory[]>([]);
  const [files, setFiles] = useState<HISTORY.FileHistory[]>([]);
  const [events, setEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载会话详情
  const loadSessionDetail = useCallback(async () => {
    if (!sessionId) return;

    setLoading(true);
    try {
      // 优先从后端获取
      let sessionData: HISTORY.SessionHistory | null = null;
      let messagesData: HISTORY.MessageHistory[] | null = null;
      let filesData: HISTORY.FileHistory[] | null = null;
      try {
        const detailResponse = await historyApi.getSessionDetail(sessionId);
        const detail = detailResponse?.data || detailResponse; // 处理API响应结构

        if (detail?.session) {
          sessionData = normalizeSessionData({
            ...detail.session,
            messageCount: detail.messages?.length || 0,
          });
        }
        if (Array.isArray(detail?.messages)) {
          messagesData = normalizeMessagesArray(detail.messages);
        }
        if (Array.isArray(detail?.files)) {
          filesData = normalizeFilesArray(detail.files);
        }
        // 事件
        try {
          const evtsResponse = await historyApi.getSessionEvents(sessionId);
          const evts = evtsResponse?.data || evtsResponse; // 处理API响应结构
          setEvents(Array.isArray(evts) ? evts.map(normalizeEventData) : []);
        } catch (e) {
          console.warn('Failed to load events:', e);
          setEvents([]);
        }
      } catch (e) {
        // 后端失败，回退本地
      }
      if (!sessionData) {
        const allSessions = getSessionHistory();
        sessionData = allSessions.find(s => s.sessionId === sessionId) || null;
      }
      if (!messagesData) {
        messagesData = getSessionMessages(sessionId);
      }
      if (!filesData) {
        filesData = getSessionFiles(sessionId);
      }

      setSession(sessionData || null);
      setMessages(messagesData || []);
      setFiles(filesData || []);
    } catch (error) {
      console.error('Failed to load session detail:', error);
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  useEffect(() => {
    loadSessionDetail();
  }, [loadSessionDetail]);

  return {
    session,
    messages,
    files,
    events,
    loading,
    refreshDetail: loadSessionDetail,
  };
}

/**
 * 历史记录统计hook
 */
export function useHistoryStats() {
  const [stats, setStats] = useState<HISTORY.HistoryStats>({
    totalSessions: 0,
    totalMessages: 0,
    totalFiles: 0,
    recentActivity: 0,
  });

  const loadStats = useCallback(() => {
    try {
      const statsData = getHistoryStats();
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  }, []);

  useEffect(() => {
    loadStats();

    // 定期更新统计信息
    const interval = setInterval(loadStats, 30000); // 30秒更新一次
    return () => clearInterval(interval);
  }, [loadStats]);

  return {
    stats,
    refreshStats: loadStats,
  };
}

/**
 * 保存聊天记录hook
 */
export function useSaveChatHistory() {
  const saveChatItem = useCallback((chatItem: CHAT.ChatItem) => {
    try {
      const { session, messages, files } = convertChatItemToHistory(chatItem);

      // 保存会话
      saveSessionHistory(session);

      // 保存消息
      messages.forEach(message => {
        saveMessageHistory(message);
      });

      // 保存文件
      files.forEach(file => {
        saveFileHistory(file);
      });
    } catch (error) {
      console.error('Failed to save chat history:', error);
    }
  }, []);

  return {
    saveChatItem,
  };
}

/**
 * 搜索建议hook
 */
export function useSearchSuggestions() {
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const getSuggestions = useCallback((keyword: string) => {
    if (!keyword || keyword.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const sessions = getSessionHistory();
      const keywordLower = keyword.toLowerCase();
      const matchedTitles = sessions
        .filter(session => session.title.toLowerCase().includes(keywordLower))
        .map(session => session.title)
        .slice(0, 5); // 最多5个建议

      setSuggestions(matchedTitles);
    } catch (error) {
      console.error('Failed to get suggestions:', error);
      setSuggestions([]);
    }
  }, []);

  return {
    suggestions,
    getSuggestions,
  };
}

/**
 * 历史记录标签管理hook
 */
export function useHistoryTags() {
  const [tags, setTags] = useState<string[]>([]);

  const loadTags = useCallback(() => {
    try {
      const sessions = getSessionHistory();
      const allTags = sessions.reduce((acc, session) => {
        session.tags.forEach(tag => {
          if (!acc.includes(tag)) {
            acc.push(tag);
          }
        });
        return acc;
      }, [] as string[]);

      setTags(allTags.sort());
    } catch (error) {
      console.error('Failed to load tags:', error);
    }
  }, []);

  const addTagToSession = useCallback((sessionId: string, tag: string) => {
    try {
      const sessions = getSessionHistory();
      const sessionIndex = sessions.findIndex(s => s.sessionId === sessionId);

      if (sessionIndex >= 0 && !sessions[sessionIndex].tags.includes(tag)) {
        sessions[sessionIndex].tags.push(tag);
        sessions[sessionIndex].updateTime = Date.now();
        saveSessionHistory(sessions[sessionIndex]);
        loadTags();
      }
    } catch (error) {
      console.error('Failed to add tag:', error);
    }
  }, [loadTags]);

  const removeTagFromSession = useCallback((sessionId: string, tag: string) => {
    try {
      const sessions = getSessionHistory();
      const sessionIndex = sessions.findIndex(s => s.sessionId === sessionId);

      if (sessionIndex >= 0) {
        sessions[sessionIndex].tags = sessions[sessionIndex].tags.filter(t => t !== tag);
        sessions[sessionIndex].updateTime = Date.now();
        saveSessionHistory(sessions[sessionIndex]);
        loadTags();
      }
    } catch (error) {
      console.error('Failed to remove tag:', error);
    }
  }, [loadTags]);

  useEffect(() => {
    loadTags();
  }, [loadTags]);

  return {
    tags,
    addTagToSession,
    removeTagFromSession,
    refreshTags: loadTags,
  };
}