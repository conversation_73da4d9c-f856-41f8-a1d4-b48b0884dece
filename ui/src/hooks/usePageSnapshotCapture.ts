/**
 * 页面快照捕获Hook
 * 用于监控ActionView状态变化并在适当时机保存快照
 */

import { useCallback, useEffect, useRef } from 'react';
import { useMemoizedFn } from 'ahooks';
import { message } from 'antd';
import { PAGE_SNAPSHOT } from '@/types/pageSnapshot';
import { captureCompletePageSnapshot, validateSnapshot } from '@/utils/pageSnapshotCapture';
import { pageSnapshotService } from '@/services/pageSnapshotService';

interface UsePageSnapshotCaptureOptions {
  /** 会话ID */
  sessionId: string;
  /** 聊天数据 */
  chatItem: CHAT.ChatItem;
  /** 是否启用自动保存 */
  enableAutoSave?: boolean;
  /** 保存成功回调 */
  onSaveSuccess?: (snapshotId: string) => void;
  /** 保存失败回调 */
  onSaveError?: (error: string) => void;
}

interface ActionViewState {
  activeView: 'follow' | 'browser' | 'file';
  taskList: any[];
  activeTask?: any;
  currentTaskIndex?: number;
}

/**
 * 页面快照捕获Hook
 */
export function usePageSnapshotCapture(options: UsePageSnapshotCaptureOptions) {
  const {
    sessionId,
    chatItem,
    enableAutoSave = true,
    onSaveSuccess,
    onSaveError
  } = options;

  const lastSnapshotRef = useRef<string | null>(null);
  const actionViewStateRef = useRef<ActionViewState>({
    activeView: 'follow',
    taskList: [],
    activeTask: undefined,
    currentTaskIndex: 0
  });

  /**
   * 更新ActionView状态
   */
  const updateActionViewState = useCallback((newState: Partial<ActionViewState>) => {
    actionViewStateRef.current = {
      ...actionViewStateRef.current,
      ...newState
    };
  }, []);

  /**
   * 执行快照保存
   */
  const performSnapshot = useMemoizedFn(async (
    type: 'auto' | 'manual' = 'auto',
    saveReason: 'task_completed' | 'user_manual' | 'auto_interval' = 'task_completed'
  ) => {
    try {
      // 1. 捕获当前页面状态
      const snapshot = captureCompletePageSnapshot(
        sessionId,
        chatItem,
        actionViewStateRef.current,
        { type, saveReason }
      );

      // 2. 验证快照数据
      const validation = validateSnapshot(snapshot);
      if (!validation.isValid) {
        const error = `快照数据验证失败: ${validation.errors.join(', ')}`;
        console.error(error);
        onSaveError?.(error);
        return null;
      }

      // 3. 显示警告（如果有）
      if (validation.warnings.length > 0) {
        console.warn('快照数据警告:', validation.warnings);
      }

      // 4. 保存快照
      const result = await pageSnapshotService.saveSnapshot(snapshot);

      if (result.success) {
        lastSnapshotRef.current = result.snapshotId || snapshot.id;
        
        // 显示保存成功提示
        const config = pageSnapshotService.getConfig();
        if (config.showSaveConfirmation) {
          message.success({
            content: `页面状态已保存 (${result.savedTo.join(', ')})`,
            duration: 3
          });
        }

        onSaveSuccess?.(result.snapshotId || snapshot.id);
        return snapshot.id;
      } else {
        const error = result.error || '快照保存失败';
        console.error(error);
        onSaveError?.(error);
        
        message.error({
          content: error,
          duration: 5
        });
        return null;
      }

    } catch (error) {
      const errorMsg = `快照保存异常: ${error}`;
      console.error(errorMsg);
      onSaveError?.(errorMsg);
      
      message.error({
        content: errorMsg,
        duration: 5
      });
      return null;
    }
  });

  /**
   * 手动保存快照
   */
  const saveManualSnapshot = useCallback(() => {
    return performSnapshot('manual', 'user_manual');
  }, [performSnapshot]);

  /**
   * 检查是否应该自动保存
   */
  const shouldAutoSave = useCallback((chatItem: CHAT.ChatItem): boolean => {
    if (!enableAutoSave) return false;
    
    const config = pageSnapshotService.getConfig();
    if (!config.autoSave || !config.saveOnTaskComplete) return false;

    // 检查任务是否完成
    const isTaskCompleted = chatItem.loading === false && 
                           (chatItem.taskStatus === 3 || chatItem.taskStatus === 2);
    
    // 检查是否有最终结果
    const hasFinalResults = chatItem.tasks && chatItem.tasks.length > 0;
    
    return isTaskCompleted && hasFinalResults;
  }, [enableAutoSave]);

  /**
   * 监控聊天状态变化，自动保存快照
   */
  useEffect(() => {
    if (!chatItem || !sessionId) return;

    // 检查是否应该自动保存
    if (shouldAutoSave(chatItem)) {
      // 防止重复保存
      const currentSnapshotKey = `${sessionId}_${chatItem.taskStatus}_${chatItem.loading}`;
      if (lastSnapshotRef.current !== currentSnapshotKey) {
        console.log('任务完成，自动保存页面快照...');
        performSnapshot('auto', 'task_completed');
        lastSnapshotRef.current = currentSnapshotKey;
      }
    }
  }, [
    chatItem?.loading,
    chatItem?.taskStatus,
    chatItem?.tasks?.length,
    sessionId,
    shouldAutoSave,
    performSnapshot
  ]);

  /**
   * 获取最后保存的快照ID
   */
  const getLastSnapshotId = useCallback(() => {
    return lastSnapshotRef.current;
  }, []);

  /**
   * 清除快照记录
   */
  const clearSnapshotRecord = useCallback(() => {
    lastSnapshotRef.current = null;
  }, []);

  /**
   * 获取当前ActionView状态
   */
  const getCurrentActionViewState = useCallback(() => {
    return { ...actionViewStateRef.current };
  }, []);

  return {
    // 状态管理
    updateActionViewState,
    getCurrentActionViewState,
    
    // 快照操作
    saveManualSnapshot,
    performSnapshot,
    
    // 状态查询
    getLastSnapshotId,
    clearSnapshotRecord,
    shouldAutoSave: shouldAutoSave(chatItem),
    
    // 配置管理
    getConfig: pageSnapshotService.getConfig,
    updateConfig: pageSnapshotService.updateConfig
  };
}

/**
 * ActionView状态监控Hook
 * 专门用于ActionView组件内部的状态监控
 */
export function useActionViewStateMonitor(
  activeView: 'follow' | 'browser' | 'file',
  taskList: any[],
  activeTask?: any,
  currentTaskIndex?: number
) {
  const stateRef = useRef<ActionViewState>({
    activeView,
    taskList,
    activeTask,
    currentTaskIndex
  });

  // 更新状态引用
  useEffect(() => {
    stateRef.current = {
      activeView,
      taskList,
      activeTask,
      currentTaskIndex
    };
  }, [activeView, taskList, activeTask, currentTaskIndex]);

  /**
   * 获取当前状态
   */
  const getCurrentState = useCallback(() => {
    return { ...stateRef.current };
  }, []);

  /**
   * 状态变化监听器
   */
  const addStateChangeListener = useCallback((
    listener: (state: ActionViewState) => void
  ) => {
    const handleStateChange = () => {
      listener(stateRef.current);
    };

    // 监听状态变化
    const interval = setInterval(handleStateChange, 1000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  return {
    getCurrentState,
    addStateChangeListener
  };
}
