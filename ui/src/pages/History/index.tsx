// 历史记录主页组件
// 新增功能：历史记录查看页面

import { memo, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Input,
  Select,
  DatePicker,
  Button,
  Card,
  List,
  Tag,
  Space,
  Tooltip,
  Modal,
  message,
  Empty,
  Pagination,
  Row,
  Col,
  Statistic,
  Upload,
} from 'antd';
import {
  SearchOutlined,
  DeleteOutlined,
  ExportOutlined,
  ClearOutlined,
  EyeOutlined,
  CalendarOutlined,
  MessageOutlined,
  FileTextOutlined,
  ArrowLeftOutlined,
  HomeOutlined,
  ImportOutlined,
} from '@ant-design/icons';
import { useHistoryList, useHistoryStats, useHistoryTags } from '@/hooks/useHistory';
import { formatTime } from '@/utils';
import { historyApi } from '@/services/history';

const { RangePicker } = DatePicker;
const { Option } = Select;

type HistoryProps = Record<string, never>;

const History: GenieType.FC<HistoryProps> = memo(() => {
  const navigate = useNavigate();
  const [messageApi] = message.useMessage();
  
  const {
    sessions,
    loading,
    // filter, // 暂时注释掉未使用的变量
    pagination,
    updateFilter,
    updatePagination,
    deleteSession,
    // refreshSessions, // 暂时注释掉未使用的变量
    cleanExpired,
    exportData,
  } = useHistoryList();
  
  const { stats } = useHistoryStats();
  const { tags } = useHistoryTags();
  
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    updateFilter({ keyword: value });
  }, [updateFilter]);

  // 处理日期范围筛选
  const handleDateRangeChange = useCallback((dates: any) => {
    if (dates && dates.length === 2) {
      updateFilter({
        dateRange: [dates[0].valueOf(), dates[1].valueOf()],
      });
    } else {
      updateFilter({ dateRange: undefined });
    }
  }, [updateFilter]);

  // 处理状态筛选
  const handleStatusChange = useCallback((status: HISTORY.SessionHistory['status'][]) => {
    updateFilter({ status });
  }, [updateFilter]);

  // 处理标签筛选
  const handleTagsChange = useCallback((selectedTags: string[]) => {
    updateFilter({ tags: selectedTags });
  }, [updateFilter]);

  // 处理排序
  const handleSortChange = useCallback((value: string) => {
    const [sortBy, sortOrder] = value.split('_') as [HISTORY.SearchFilter['sortBy'], HISTORY.SearchFilter['sortOrder']];
    updateFilter({ sortBy, sortOrder });
  }, [updateFilter]);

  // 查看会话详情
  const handleViewSession = useCallback((sessionId: string) => {
    // 对sessionId进行URL编码，处理特殊字符（如冒号）
    const encodedSessionId = encodeURIComponent(sessionId);
    navigate(`/history/session/${encodedSessionId}`);
  }, [navigate]);

  // 确认删除会话
  const handleDeleteConfirm = useCallback((sessionId: string) => {
    setSessionToDelete(sessionId);
    setDeleteModalVisible(true);
  }, []);

  // 执行删除
  const handleDeleteExecute = useCallback(async () => {
    if (sessionToDelete) {
      try {
        await deleteSession(sessionToDelete);
        messageApi.success('会话删除成功');
      } catch (error) {
        messageApi.error('删除失败，请重试');
      }
    }
    setDeleteModalVisible(false);
    setSessionToDelete(null);
  }, [sessionToDelete, deleteSession, messageApi]);

  // 清理过期数据
  const handleCleanExpired = useCallback(async () => {
    try {
      await cleanExpired();
      messageApi.success('过期数据清理完成');
    } catch (error) {
      messageApi.error('清理失败，请重试');
    }
  }, [cleanExpired, messageApi]);

  // 导出数据
  const handleExport = useCallback(() => {
    try {
      exportData();
      messageApi.success('数据导出成功');
    } catch (error) {
      messageApi.error('导出失败，请重试');
    }
  }, [exportData, messageApi]);

  // 获取状态标签颜色
  const getStatusColor = (status: HISTORY.SessionHistory['status']) => {
    switch (status) {
      case 'completed': return 'green';
      case 'running': return 'blue';
      case 'error': return 'red';
      default: return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: HISTORY.SessionHistory['status']) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'running': return '运行中';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 页面标题和统计 */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <Button
                icon={<HomeOutlined />}
                onClick={() => navigate('/')}
                type="text"
                size="large"
                title="返回主页"
              >
                返回主页
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">历史记录</h1>
                <p className="text-gray-500 mt-1">查看和管理您的对话历史</p>
              </div>
            </div>
            <Space>
              <Button
                icon={<ClearOutlined />}
                onClick={handleCleanExpired}
                title="清理过期数据"
              >
                清理
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExport}
                title="导出历史数据"
              >
                导出
              </Button>
              <Upload
                accept="application/json"
                showUploadList={false}
                beforeUpload={async (file) => {
                  try {
                    const text = await file.text();
                    const json = JSON.parse(text);
                    await historyApi.importHistory(json);
                    messageApi.success('导入成功');
                  } catch (e) {
                    messageApi.error('导入失败');
                  }
                  return false;
                }}
              >
                <Button icon={<ImportOutlined />} title="导入历史数据(JSON)">导入</Button>
              </Upload>
            </Space>
          </div>
          
          {/* 统计卡片 */}
          <Row gutter={16}>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="总会话数"
                  value={stats.totalSessions}
                  prefix={<MessageOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="总消息数"
                  value={stats.totalMessages}
                  prefix={<FileTextOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="生成文件"
                  value={stats.totalFiles}
                  prefix={<FileTextOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="近期活动"
                  value={stats.recentActivity}
                  prefix={<CalendarOutlined />}
                  suffix="/ 7天"
                />
              </Card>
            </Col>
          </Row>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="max-w-7xl mx-auto">
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Input.Search
                placeholder="搜索会话标题或内容..."
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
                prefix={<SearchOutlined />}
              />
            </Col>
            <Col>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                onChange={handleDateRangeChange}
                style={{ width: 240 }}
              />
            </Col>
            <Col>
              <Select
                mode="multiple"
                placeholder="状态筛选"
                style={{ width: 120 }}
                onChange={handleStatusChange}
                allowClear
              >
                <Option value="completed">已完成</Option>
                <Option value="running">运行中</Option>
                <Option value="error">错误</Option>
              </Select>
            </Col>
            <Col>
              <Select
                mode="multiple"
                placeholder="标签筛选"
                style={{ width: 120 }}
                onChange={handleTagsChange}
                allowClear
              >
                {tags.map(tag => (
                  <Option key={tag} value={tag}>{tag}</Option>
                ))}
              </Select>
            </Col>
            <Col>
              <Select
                placeholder="排序方式"
                style={{ width: 120 }}
                onChange={handleSortChange}
                defaultValue="updateTime_desc"
              >
                <Option value="updateTime_desc">最近更新</Option>
                <Option value="createTime_desc">最近创建</Option>
                <Option value="messageCount_desc">消息最多</Option>
                <Option value="updateTime_asc">最早更新</Option>
              </Select>
            </Col>
          </Row>
        </div>
      </div>

      {/* 会话列表 */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto p-6">
          {sessions.length === 0 ? (
            <Empty
              description="暂无历史记录"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            <List
              loading={loading}
              dataSource={sessions}
              renderItem={(session) => (
                <List.Item key={session.id}>
                  <Card
                    hoverable
                    className="w-full"
                    styles={{ body: { padding: '16px' } }}
                    actions={[
                      <Tooltip title="查看详情">
                        <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleViewSession(session.sessionId)}
                        />
                      </Tooltip>,
                      <Tooltip title="删除会话">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteConfirm(session.sessionId)}
                        />
                      </Tooltip>,
                    ]}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-medium text-gray-900 truncate mb-2">
                          {session.title}
                        </h3>
                        {session.summary && (
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {session.summary}
                          </p>
                        )}
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>
                            <CalendarOutlined className="mr-1" />
                            {formatTime(session.updateTime)}
                          </span>
                          <span>
                            <MessageOutlined className="mr-1" />
                            {session.messageCount} 条消息
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-2 ml-4">
                        <Tag color={getStatusColor(session.status)}>
                          {getStatusText(session.status)}
                        </Tag>
                        {session.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {session.tags.slice(0, 3).map(tag => (
                              <Tag key={tag}>{tag}</Tag>
                            ))}
                            {session.tags.length > 3 && (
                              <Tag>+{session.tags.length - 3}</Tag>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                </List.Item>
              )}
            />
          )}
          
          {/* 分页 */}
          {pagination.total && pagination.total > 0 && (
            <div className="flex justify-center mt-6">
              <Pagination
                current={pagination.page}
                pageSize={pagination.pageSize}
                total={pagination.total}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                onChange={(page, pageSize) => updatePagination({ page, pageSize })}
              />
            </div>
          )}
        </div>
      </div>

      {/* 删除确认弹窗 */}
      <Modal
        title="确认删除"
        open={deleteModalVisible}
        onOk={handleDeleteExecute}
        onCancel={() => setDeleteModalVisible(false)}
        okText="删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要删除这个会话吗？此操作不可撤销。</p>
      </Modal>
    </div>
  );
});

History.displayName = 'History';

export default History;