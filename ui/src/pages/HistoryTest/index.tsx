/**
 * 历史回放测试页面
 * 用于测试和验证历史数据解析和显示功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Alert,
  Spin,
  Select,
  message,
} from 'antd';
import {
  PlayCircleOutlined,
  ReloadOutlined,
  BugOutlined,
} from '@ant-design/icons';
import HistoryReplay from '@/components/HistoryReplay';

const { Title, Text } = Typography;
const { Option } = Select;

// 模拟真实的历史数据
const mockHistoryData = {
  session: {
    sessionId: 'test-session-123',
    title: '测试会话 - 临床影像检查指南查询',
    status: 'completed',
    createTime: Date.now() - 3600000, // 1小时前
    updateTime: Date.now() - 1800000, // 30分钟前
  },
  messages: [
    {
      id: 'msg-1',
      sessionId: 'test-session-123',
      type: 'user',
      role: 'user',
      content: '国内和国外对应临床影像检查适应性评价的指南，在哪些网站查找有最全的信息',
      timestamp: Date.now() - 3600000,
    },
    {
      id: 'msg-2',
      sessionId: 'test-session-123',
      type: 'assistant',
      role: 'assistant',
      content: '我将为您查找国内外临床影像检查适应性评价指南的权威网站和数据库...',
      timestamp: Date.now() - 1800000,
    }
  ],
  events: [
    {
      id: 'evt-1',
      sessionId: 'test-session-123',
      messageType: 'tool_thought',
      messageOrder: 1,
      taskId: 'task-123',
      taskOrder: 1,
      isFinal: false,
      payloadJson: JSON.stringify({
        eventData: {
          messageType: 'tool_thought',
          resultMap: {
            agentType: 5,
            messageType: 'tool_thought',
            toolThought: '用户询问临床影像检查适应性评价指南的查找网站，这是一个专业的医学信息检索需求。我需要分析国内外权威的医学指南数据库和网站。',
            isFinal: false,
            finish: false
          }
        }
      }),
      createTime: Date.now() - 3500000,
    },
    {
      id: 'evt-2',
      sessionId: 'test-session-123',
      messageType: 'deep_search',
      messageOrder: 2,
      taskId: 'task-123',
      taskOrder: 2,
      isFinal: false,
      payloadJson: JSON.stringify({
        eventData: {
          messageType: 'deep_search',
          resultMap: {
            messageType: 'deep_search',
            query: '临床影像检查适应性评价 指南 网站 国内 国际 最全',
            resultMap: {
              searchResult: {
                query: [
                  '国内临床影像检查适应性评价指南网站',
                  '国际影像学指南数据库平台',
                  'ACR Appropriateness Criteria 指南',
                  '中华医学会影像学分会指南'
                ],
                docs: [
                  [
                    {
                      title: '中华医学会影像学分会官网',
                      link: 'https://www.cma.org.cn/imaging',
                      content: '中华医学会影像学分会发布的各类影像检查适应性指南和专家共识，涵盖CT、MRI、超声等多种影像技术的临床应用规范。'
                    },
                    {
                      title: 'ACR Appropriateness Criteria',
                      link: 'https://www.acr.org/Clinical-Resources/ACR-Appropriateness-Criteria',
                      content: '美国放射学会发布的影像检查适应性标准，是国际上最权威的影像学指南之一，提供基于循证医学的影像检查推荐。'
                    }
                  ]
                ],
                searchFinish: true
              }
            },
            answer: '# 临床影像检查适应性评价指南及数据库网站汇总\n\n## 国内权威网站\n\n1. **中华医学会影像学分会**\n   - 网址：https://www.cma.org.cn/imaging\n   - 内容：发布各类影像检查适应性指南\n\n2. **国家卫健委官网**\n   - 发布国家级临床诊疗指南\n\n## 国际权威网站\n\n1. **ACR Appropriateness Criteria**\n   - 网址：https://www.acr.org/Clinical-Resources/ACR-Appropriateness-Criteria\n   - 内容：美国放射学会影像检查适应性标准\n\n2. **European Society of Radiology (ESR)**\n   - 欧洲放射学会指南\n\n这些网站提供了最全面和权威的临床影像检查适应性评价指南。'
          }
        }
      }),
      createTime: Date.now() - 3000000,
    },
    {
      id: 'evt-3',
      sessionId: 'test-session-123',
      messageType: 'result',
      messageOrder: 3,
      taskId: 'task-123',
      taskOrder: 3,
      isFinal: true,
      payloadJson: JSON.stringify({
        eventData: {
          messageType: 'result',
          resultMap: {
            result: '已为您整理了国内外临床影像检查适应性评价指南的权威查找网站，包括中华医学会、ACR等权威机构的官方平台。',
            messageType: 'result',
            resultMap: {
              taskSummary: '成功整理了国内外临床影像检查适应性评价指南的权威网站和数据库，为用户提供了完整的信息检索指南。',
              fileList: [
                {
                  fileName: '临床影像检查适应性评价指南网站汇总.md',
                  fileSize: 15680,
                  description: '详细汇总了国内外权威的临床影像检查适应性评价指南网站和数据库平台'
                }
              ]
            },
            isFinal: true,
            finish: true
          }
        }
      }),
      createTime: Date.now() - 1800000,
    }
  ],
  files: [
    {
      id: 'file-1',
      sessionId: 'test-session-123',
      fileName: '临床影像检查适应性评价指南网站汇总.md',
      fileType: 'markdown',
      size: 15680,
      downloadUrl: 'http://example.com/download/file-1',
      createTime: Date.now() - 1800000,
    }
  ]
};

const HistoryTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [testData, setTestData] = useState(mockHistoryData);
  const [selectedTest, setSelectedTest] = useState('basic');

  // 模拟加载真实数据
  const loadRealData = async () => {
    setLoading(true);
    try {
      // 这里可以调用真实的API
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('测试数据加载成功');
    } catch (error) {
      message.error('加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置测试数据
  const resetTestData = () => {
    setTestData(mockHistoryData);
    message.info('测试数据已重置');
  };

  // 运行数据解析测试
  const runParsingTest = () => {
    console.log('=== 历史数据解析测试 ===');
    console.log('Session:', testData.session);
    console.log('Messages:', testData.messages);
    console.log('Events:', testData.events);
    console.log('Files:', testData.files);
    
    // 测试事件数据解析
    testData.events.forEach((event, index) => {
      try {
        const payload = JSON.parse(event.payloadJson);
        console.log(`Event ${index + 1} (${event.messageType}):`, payload);
      } catch (error) {
        console.error(`Event ${index + 1} 解析失败:`, error);
      }
    });
    
    message.success('数据解析测试完成，请查看控制台输出');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部控制面板 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <Title level={3} className="mb-2">历史回放功能测试</Title>
              <Text type="secondary">测试和验证历史数据解析与显示功能</Text>
            </div>
            
            <Space>
              <Select
                value={selectedTest}
                onChange={setSelectedTest}
                style={{ width: 150 }}
              >
                <Option value="basic">基础测试</Option>
                <Option value="complex">复杂数据</Option>
                <Option value="error">错误处理</Option>
              </Select>
              
              <Button
                icon={<BugOutlined />}
                onClick={runParsingTest}
              >
                解析测试
              </Button>
              
              <Button
                icon={<ReloadOutlined />}
                onClick={resetTestData}
              >
                重置数据
              </Button>
              
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={loading}
                onClick={loadRealData}
              >
                加载真实数据
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 测试信息面板 */}
      <div className="max-w-7xl mx-auto p-4">
        <Alert
          message="测试说明"
          description={
            <div>
              <p>• 此页面用于测试历史回放组件的数据解析和显示功能</p>
              <p>• 使用模拟的真实数据结构进行测试</p>
              <p>• 可以在浏览器控制台查看详细的解析日志</p>
              <p>• 测试数据包含：工具思考、深度搜索、执行结果等事件类型</p>
            </div>
          }
          type="info"
          showIcon
          className="mb-4"
        />
      </div>

      {/* 历史回放组件 */}
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm m-4 h-[calc(100vh-300px)]">
          <Spin spinning={loading}>
            <HistoryReplay
              session={testData.session}
              messages={testData.messages}
              events={testData.events}
              files={testData.files}
              loading={loading}
            />
          </Spin>
        </div>
      </div>
    </div>
  );
};

export default HistoryTest;
