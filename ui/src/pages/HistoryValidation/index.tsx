// 历史数据验证页面
// 用于诊断和验证历史数据的完整性

import React, { useState, useCallback } from 'react';
import {
  Card,
  Button,
  Table,
  Tag,
  Alert,
  Spin,
  Input,
  Space,
  Typography,
  Collapse,
  Statistic,
  Row,
  Col,
  message,
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { historyApi } from '@/services/history';

const { Title, Text } = Typography;
const { Panel } = Collapse;
const { TextArea } = Input;

interface ValidationResult {
  sessionId: string;
  errors: string[];
  warnings: string[];
  messageCount: number;
  eventCount: number;
  eventTypeCounts: Record<string, number>;
  valid: boolean;
  hasIssues: boolean;
}

const HistoryValidation: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [sessionIds, setSessionIds] = useState('');
  const [validationResults, setValidationResults] = useState<Record<string, ValidationResult>>({});
  const [selectedSession, setSelectedSession] = useState<string | null>(null);

  // 验证单个会话
  const validateSingleSession = useCallback(async (sessionId: string) => {
    try {
      const response = await historyApi.validateSession(sessionId);
      return response.data;
    } catch (error) {
      console.error('Failed to validate session:', error);
      throw error;
    }
  }, []);

  // 批量验证会话
  const validateMultipleSessions = useCallback(async (sessionIdList: string[]) => {
    try {
      const response = await historyApi.validateMultipleSessions(sessionIdList);
      return response.data;
    } catch (error) {
      console.error('Failed to validate sessions:', error);
      throw error;
    }
  }, []);

  // 执行验证
  const handleValidation = useCallback(async () => {
    if (!sessionIds.trim()) {
      message.warning('请输入会话ID');
      return;
    }

    setLoading(true);
    try {
      const sessionIdList = sessionIds
        .split('\n')
        .map(id => id.trim())
        .filter(id => id.length > 0);

      if (sessionIdList.length === 0) {
        message.warning('请输入有效的会话ID');
        return;
      }

      let results: Record<string, ValidationResult>;

      if (sessionIdList.length === 1) {
        const result = await validateSingleSession(sessionIdList[0]);
        results = { [sessionIdList[0]]: result };
      } else {
        results = await validateMultipleSessions(sessionIdList);
      }

      setValidationResults(results);
      message.success(`验证完成，共检查 ${Object.keys(results).length} 个会话`);
    } catch (error) {
      message.error('验证失败：' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  }, [sessionIds, validateSingleSession, validateMultipleSessions]);

  // 清空结果
  const handleClear = useCallback(() => {
    setValidationResults({});
    setSelectedSession(null);
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '会话ID',
      dataIndex: 'sessionId',
      key: 'sessionId',
      width: 200,
      render: (sessionId: string) => (
        <Text copyable={{ text: sessionId }} style={{ fontSize: '12px' }}>
          {sessionId.substring(0, 8)}...
        </Text>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: ValidationResult) => {
        if (record.errors.length > 0) {
          return <Tag color="error" icon={<ExclamationCircleOutlined />}>错误</Tag>;
        }
        if (record.warnings.length > 0) {
          return <Tag color="warning" icon={<WarningOutlined />}>警告</Tag>;
        }
        return <Tag color="success" icon={<CheckCircleOutlined />}>正常</Tag>;
      },
    },
    {
      title: '消息数',
      dataIndex: 'messageCount',
      key: 'messageCount',
      width: 80,
    },
    {
      title: '事件数',
      dataIndex: 'eventCount',
      key: 'eventCount',
      width: 80,
    },
    {
      title: '问题数',
      key: 'issueCount',
      width: 80,
      render: (record: ValidationResult) => (
        <Text type={record.errors.length > 0 ? 'danger' : record.warnings.length > 0 ? 'warning' : 'secondary'}>
          {record.errors.length + record.warnings.length}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (record: ValidationResult) => (
        <Button
          size="small"
          onClick={() => setSelectedSession(record.sessionId)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  // 统计信息
  const stats = React.useMemo(() => {
    const results = Object.values(validationResults);
    const totalSessions = results.length;
    const errorSessions = results.filter(r => r.errors.length > 0).length;
    const warningSessions = results.filter(r => r.warnings.length > 0 && r.errors.length === 0).length;
    const healthySessions = results.filter(r => r.errors.length === 0 && r.warnings.length === 0).length;
    const totalMessages = results.reduce((sum, r) => sum + r.messageCount, 0);
    const totalEvents = results.reduce((sum, r) => sum + r.eventCount, 0);

    return {
      totalSessions,
      errorSessions,
      warningSessions,
      healthySessions,
      totalMessages,
      totalEvents,
    };
  }, [validationResults]);

  const selectedResult = selectedSession ? validationResults[selectedSession] : null;

  return (
    <div className="p-6">
      <Title level={2}>历史数据验证</Title>

      <Card className="mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>会话ID列表（每行一个）：</Text>
            <TextArea
              rows={4}
              value={sessionIds}
              onChange={(e) => setSessionIds(e.target.value)}
              placeholder="请输入会话ID，每行一个&#10;例如：&#10;session-123&#10;session-456"
            />
          </div>

          <Space>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              loading={loading}
              onClick={handleValidation}
            >
              开始验证
            </Button>
            <Button onClick={handleClear}>清空结果</Button>
          </Space>
        </Space>
      </Card>

      {Object.keys(validationResults).length > 0 && (
        <>
          {/* 统计信息 */}
          <Card className="mb-4">
            <Row gutter={16}>
              <Col span={4}>
                <Statistic title="总会话数" value={stats.totalSessions} />
              </Col>
              <Col span={4}>
                <Statistic
                  title="错误会话"
                  value={stats.errorSessions}
                  valueStyle={{ color: stats.errorSessions > 0 ? '#cf1322' : undefined }}
                />
              </Col>
              <Col span={4}>
                <Statistic
                  title="警告会话"
                  value={stats.warningSessions}
                  valueStyle={{ color: stats.warningSessions > 0 ? '#d48806' : undefined }}
                />
              </Col>
              <Col span={4}>
                <Statistic
                  title="正常会话"
                  value={stats.healthySessions}
                  valueStyle={{ color: '#389e0d' }}
                />
              </Col>
              <Col span={4}>
                <Statistic title="总消息数" value={stats.totalMessages} />
              </Col>
              <Col span={4}>
                <Statistic title="总事件数" value={stats.totalEvents} />
              </Col>
            </Row>
          </Card>

          {/* 验证结果表格 */}
          <Card title="验证结果" className="mb-4">
            <Table
              columns={columns}
              dataSource={Object.values(validationResults)}
              rowKey="sessionId"
              size="small"
              pagination={{ pageSize: 10 }}
            />
          </Card>

          {/* 详细信息 */}
          {selectedResult && (
            <Card title={`会话详情: ${selectedResult.sessionId}`}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {selectedResult.errors.length > 0 && (
                  <Alert
                    message="错误"
                    description={
                      <ul>
                        {selectedResult.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    }
                    type="error"
                    showIcon
                  />
                )}

                {selectedResult.warnings.length > 0 && (
                  <Alert
                    message="警告"
                    description={
                      <ul>
                        {selectedResult.warnings.map((warning, index) => (
                          <li key={index}>{warning}</li>
                        ))}
                      </ul>
                    }
                    type="warning"
                    showIcon
                  />
                )}

                <Collapse>
                  <Panel header="事件类型统计" key="eventTypes">
                    <Row gutter={[16, 16]}>
                      {Object.entries(selectedResult.eventTypeCounts).map(([type, count]) => (
                        <Col span={6} key={type}>
                          <Statistic title={type} value={count} />
                        </Col>
                      ))}
                    </Row>
                  </Panel>
                </Collapse>
              </Space>
            </Card>
          )}
        </>
      )}
    </div>
  );
};

export default HistoryValidation;
