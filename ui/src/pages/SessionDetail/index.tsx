// 会话详情页组件
// 新增功能：历史记录会话详情查看

import { memo, useState, useCallback, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Button,
  Card,
  List,
  Tag,
  Tooltip,
  Avatar,
  Typography,
  Empty,
  Spin,
  Row,
  Col,
  Timeline,
  Collapse,
  Badge,
  Checkbox,
  Select,
} from 'antd';

const { Option } = Select;
import {
  ArrowLeftOutlined,
  UserOutlined,
  RobotOutlined,
  FileTextOutlined,
  DownloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { useSessionDetail } from '@/hooks/useHistory';
import { formatTime } from '@/utils';
// import { ActionPanel } from '@/components/ActionPanel'; // 暂时注释掉未使用的导入
import { PlanView } from '@/components/PlanView';
// 引入数据验证工具用于生产环境验证
import '@/utils/dataValidation';

const { Text, Paragraph, Title } = Typography;
const { Panel } = Collapse;

type SessionDetailProps = Record<string, never>;

const SessionDetail: GenieType.FC<SessionDetailProps> = memo(() => {
  const { sessionId: rawSessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const [expandedPanels, setExpandedPanels] = useState<string[]>([]);
  
  // 解码sessionId，处理URL编码的特殊字符
  const sessionId = rawSessionId ? decodeURIComponent(rawSessionId) : '';
  
  const { session, messages, files, events, loading, refreshDetail } = useSessionDetail(sessionId);
  
  // 事件过滤状态
  const [showHeartbeat, setShowHeartbeat] = useState(false);
  const [eventFilter, setEventFilter] = useState('all');
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());
  
  // 过滤事件数据
  const filteredEvents = events?.filter((evt: any) => {
    // 过滤heartbeat事件
    if (!showHeartbeat && evt.messageType === 'heartbeat') {
      return false;
    }
    
    // 按类型过滤
    if (eventFilter !== 'all' && evt.messageType !== eventFilter) {
      return false;
    }
    
    return true;
  }) || [];
  
  // 切换事件展开状态
  const toggleEventExpansion = (eventId: string) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedEvents(newExpanded);
  };
  
  // 获取事件摘要信息
  const getEventSummary = (evt: any) => {
    if (evt.messageType === 'tool_thought') {
      return '🤔 思考中...';
    } else if (evt.messageType === 'deep_search') {
      try {
        const obj = JSON.parse(evt.payloadJson || '{}');
        const messageOrder = obj.eventData?.messageOrder || 0;
        const searchFinish = obj.eventData?.resultMap?.resultMap?.searchFinish;
        const queryCount = obj.eventData?.resultMap?.resultMap?.searchResult?.query?.length || 0;
        const docCount = obj.eventData?.resultMap?.resultMap?.searchResult?.docs?.flat().length || 0;
        return `🔍 第${messageOrder}步 - ${queryCount}个查询 - ${docCount}个文档 ${searchFinish ? '✅' : '⏳'}`;
      } catch {
        return '🔍 深度搜索';
      }
    } else if (evt.messageType === 'result') {
      try {
        const obj = JSON.parse(evt.payloadJson || '{}');
        const fileCount = obj.eventData?.resultMap?.resultMap?.fileList?.length || 0;
        return `🎉 任务完成 - 生成${fileCount}个文件`;
      } catch {
        return '🎉 任务完成';
      }
    } else if (evt.messageType === 'heartbeat') {
      return '💓 心跳';
    }
    return `📝 ${evt.messageType || '未知'}`;
  };
  
  // 监听数据更新事件
  useEffect(() => {
    const handleSessionDataUpdate = (event: CustomEvent) => {
      if (event.detail?.sessionId === sessionId) {
        console.log('检测到数据更新，重新加载会话详情...');
        refreshDetail();
      }
    };
    
    window.addEventListener('sessionDataUpdated', handleSessionDataUpdate as EventListener);
    
    return () => {
      window.removeEventListener('sessionDataUpdated', handleSessionDataUpdate as EventListener);
    };
  }, [sessionId, refreshDetail]);

  // 返回历史记录列表
  const handleBack = useCallback(() => {
    navigate('/history');
  }, [navigate]);

  // 下载文件
  const handleDownloadFile = useCallback((file: HISTORY.FileHistory) => {
    if (file.downloadUrl) {
      const link = document.createElement('a');
      link.href = file.downloadUrl;
      link.download = file.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, []);

  // 获取消息类型图标
  const getMessageIcon = (type: HISTORY.MessageHistory['type']) => {
    switch (type) {
      case 'user':
        return <Avatar icon={<UserOutlined />} style={{ backgroundColor: '#4040ff' }} />;
      case 'assistant':
        return <Avatar icon={<RobotOutlined />} style={{ backgroundColor: '#52c41a' }} />;
      case 'system':
        return <Avatar icon={<ExclamationCircleOutlined />} style={{ backgroundColor: '#faad14' }} />;
      default:
        return <Avatar icon={<UserOutlined />} />;
    }
  };

  // 获取任务状态图标
  const getTaskStatusIcon = (status?: MESSAGE.MsgItem['taskStatus']) => {
    switch (status) {
      case 1: // running
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 2: // success
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 3: // error
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: HISTORY.SessionHistory['status']) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'running': return '运行中';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: HISTORY.SessionHistory['status']) => {
    switch (status) {
      case 'completed': return 'green';
      case 'running': return 'blue';
      case 'error': return 'red';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="h-full flex flex-col items-center justify-center">
        <Empty description="会话不存在" />
        <Button type="primary" onClick={handleBack} className="mt-4">
          返回历史记录
        </Button>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleBack}
                type="text"
              >
                返回
              </Button>

              <div>
                <Title level={3} className="mb-0">{session.title}</Title>
                <div className="flex items-center space-x-4 mt-2">
                  <Tag color={getStatusColor(session.status)}>
                    {getStatusText(session.status)}
                  </Tag>
                  <Text type="secondary">
                    <ClockCircleOutlined className="mr-1" />
                    创建于 {formatTime(session.createTime)}
                  </Text>
                  <Text type="secondary">
                    更新于 {formatTime(session.updateTime)}
                  </Text>
                  <Text type="secondary">
                    {session.messageCount} 条消息
                  </Text>
                </div>
              </div>
            </div>
            {session.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {session.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="max-w-7xl mx-auto h-full">
          <Row gutter={16} className="h-full">
            {/* 消息时间线 + 事件回放 */}
            <Col span={16} className="h-full">
              <div className="bg-white rounded-lg shadow-sm m-4 h-[calc(100%-2rem)] overflow-auto">
                <div className="p-6">
                  <Title level={4} className="mb-4">对话记录</Title>
                  {messages.length === 0 ? (
                    <Empty description="暂无消息记录" />
                  ) : (
                    <Timeline 
                      mode="left" 
                      className="mt-4"
                      items={messages.map((message) => ({
                        key: message.id,
                        dot: getMessageIcon(message.type),
                        color: message.type === 'user' ? 'blue' : 'green',
                        children: (
                          <Card
                            size="small"
                            className="mb-4"
                            title={
                              <div className="flex items-center justify-between">
                                <span>
                                  {message.type === 'user' ? '用户' : '助手'}
                                </span>
                                <Text type="secondary" className="text-xs">
                                  {formatTime(message.timestamp)}
                                </Text>
                              </div>
                            }
                          >
                            <Paragraph className="mb-2">
                              {message.content}
                            </Paragraph>
                            
                            {/* 文件附件 */}
                            {message.files && message.files.length > 0 && (
                              <div className="mt-3">
                                <Text strong className="text-sm">附件：</Text>
                                <div className="flex flex-wrap gap-2 mt-1">
                                  {message.files.map((file, fileIndex) => (
                                    <Tag key={fileIndex} icon={<FileTextOutlined />}>
                                      {file.name}
                                    </Tag>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {/* 任务信息 */}
                            {message.tasks && message.tasks.length > 0 && (
                              <div className="mt-3">
                                <Collapse
                                  size="small"
                                  activeKey={expandedPanels}
                                  onChange={setExpandedPanels}
                                >
                                  <Panel
                                    header={
                                      <div className="flex items-center">
                                        <Text strong className="text-sm">任务执行</Text>
                                        <Badge
                                          count={message.tasks.flat().length}
                                          size="small"
                                          className="ml-2"
                                        />
                                      </div>
                                    }
                                    key={`tasks_${message.id}`}
                                  >
                                    {message.tasks.map((taskGroup, groupIndex) => (
                                      <div key={groupIndex} className="mb-2">
                                        {taskGroup.map((task, taskIndex) => (
                                          <div key={taskIndex} className="flex items-center space-x-2 mb-1">
                                            {getTaskStatusIcon(message.taskStatus)}
                                            <Text className="text-sm">{task.task || '未命名任务'}</Text>
                                          </div>
                                        ))}
                                      </div>
                                    ))}
                                  </Panel>
                                </Collapse>
                              </div>
                            )}
                            
                            {/* 计划信息 */}
                            {message.plan && (
                              <div className="mt-3">
                                <Collapse size="small">
                                  <Panel header={<Text strong className="text-sm">执行计划</Text>} key="plan">
                                    <PlanView plan={message.plan} />
                                  </Panel>
                                </Collapse>
                              </div>
                            )}
                            
                            {/* 思考过程 */}
                            {(message.thought?.trim() || message.toolThought?.trim() || message.planThought?.trim()) && (
                              <div className="mt-3">
                                <Collapse size="small">
                                  <Panel header={<Text strong className="text-sm">思考过程</Text>} key="thought">
                                    {message.thought?.trim() && (
                                      <div className="mb-3">
                                        <Text strong className="text-xs text-blue-600">基础思考：</Text>
                                        <Paragraph className="text-sm text-gray-600 mt-1">
                                          {message.thought}
                                        </Paragraph>
                                      </div>
                                    )}
                                    {message.toolThought?.trim() && (
                                      <div className="mb-3">
                                        <Text strong className="text-xs text-green-600">工具思考：</Text>
                                        <Paragraph className="text-sm text-gray-600 mt-1">
                                          {message.toolThought}
                                        </Paragraph>
                                      </div>
                                    )}
                                    {message.planThought?.trim() && (
                                      <div className="mb-3">
                                        <Text strong className="text-xs text-purple-600">计划思考：</Text>
                                        <Paragraph className="text-sm text-gray-600 mt-1">
                                          {message.planThought}
                                        </Paragraph>
                                      </div>
                                    )}
                                  </Panel>
                                </Collapse>
                              </div>
                            )}
                            
                            {/* 工具调用详情 */}
                            {message.toolResult && (
                              <div className="mt-3">
                                <Collapse size="small">
                                  <Panel header={<Text strong className="text-sm">工具调用</Text>} key="toolResult">
                                    <div className="space-y-2">
                                      <div>
                                        <Text strong className="text-xs text-orange-600">工具名称：</Text>
                                        <Text className="text-sm ml-2">{message.toolResult.toolName}</Text>
                                      </div>
                                      {message.toolResult.toolParam?.query && (
                                        <div>
                                          <Text strong className="text-xs text-orange-600">查询参数：</Text>
                                          <Text className="text-sm ml-2">{message.toolResult.toolParam.query}</Text>
                                        </div>
                                      )}
                                      <div>
                                        <Text strong className="text-xs text-orange-600">执行结果：</Text>
                                        <Paragraph className="text-sm text-gray-600 mt-1 bg-gray-50 p-2 rounded">
                                          {message.toolResult.toolResult}
                                        </Paragraph>
                                      </div>
                                    </div>
                                  </Panel>
                                </Collapse>
                              </div>
                            )}
                            
                            {/* 网页搜索结果 */}
                            {message.resultMap?.searchResult && (
                              <div className="mt-3">
                                <Collapse size="small">
                                  <Panel 
                                    header={
                                      <div className="flex items-center">
                                        <Text strong className="text-sm">网页搜索结果</Text>
                                        <Badge 
                                          count={message.resultMap.searchResult.docs?.flat().length || 0} 
                                          size="small" 
                                          className="ml-2"
                                        />
                                      </div>
                                    } 
                                    key="searchResult"
                                  >
                                    {message.resultMap.searchResult.query && (
                                      <div className="mb-3">
                                        <Text strong className="text-xs text-indigo-600">搜索查询：</Text>
                                        <div className="mt-1">
                                          {Array.isArray(message.resultMap.searchResult.query) ? 
                                            message.resultMap.searchResult.query.map((query, index) => (
                                              <Tag key={index} className="mb-1">{query}</Tag>
                                            )) : 
                                            <Tag className="mb-1">{message.resultMap.searchResult.query}</Tag>
                                          }
                                        </div>
                                      </div>
                                    )}
                                    {message.resultMap.searchResult.docs && (
                                      <div>
                                        <Text strong className="text-xs text-indigo-600">搜索结果：</Text>
                                        <div className="mt-2 space-y-2">
                                          {Array.isArray(message.resultMap.searchResult.docs) ? 
                                            message.resultMap.searchResult.docs.map((docGroup, groupIndex) => (
                                              <div key={groupIndex}>
                                                {Array.isArray(docGroup) ? docGroup.map((doc, docIndex) => (
                                                  <Card key={docIndex} size="small" className="mb-2">
                                                    <div className="space-y-1">
                                                      <div>
                                                        <Text strong className="text-xs">{doc.title || '未知标题'}</Text>
                                                      </div>
                                                      <div>
                                                        <Text className="text-xs text-gray-500">{doc.link || '无链接'}</Text>
                                                      </div>
                                                      <div>
                                                        <Text className="text-xs text-gray-600">
                                                          {doc.content ? (doc.content.length > 200 ? `${doc.content.substring(0, 200)}...` : doc.content) : '无内容'}
                                                        </Text>
                                                      </div>
                                                    </div>
                                                  </Card>
                                                )) : (
                                                  <Card size="small" className="mb-2">
                                                    <Text className="text-xs">搜索结果格式错误</Text>
                                                  </Card>
                                                )}
                                              </div>
                                            )) : (
                                              <Card size="small" className="mb-2">
                                                <Text className="text-xs">搜索结果数据格式错误</Text>
                                              </Card>
                                            )
                                          }
                                        </div>
                                      </div>
                                    )}
                                  </Panel>
                                </Collapse>
                              </div>
                            )}
                          </Card>
                        )
                      }))}
                    />
                  )}
                  {/* 过程事件回放 */}
                  <div className="mt-8">
                    <div className="flex items-center justify-between mb-4">
                      <Title level={4} className="mb-0">过程回放</Title>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={showHeartbeat}
                            onChange={(e) => setShowHeartbeat(e.target.checked)}
                            className="text-xs"
                          >
                            显示心跳
                          </Checkbox>
                          <Badge count={events?.length || 0} className="bg-blue-100" />
                        </div>
                        <Select
                          value={eventFilter}
                          onChange={setEventFilter}
                          size="small"
                          style={{ width: 120 }}
                        >
                          <Option value="all">全部事件</Option>
                          <Option value="tool_thought">工具思考</Option>
                          <Option value="deep_search">深度搜索</Option>
                          <Option value="result">任务结果</Option>
                        </Select>
                      </div>
                    </div>
                    
                    {(!events || events.length === 0) ? (
                      <Empty description="暂无过程事件" />
                    ) : (
                      <div className="space-y-4">
                        {filteredEvents.map((evt: any, idx: number) => {
                          const eventId = evt.id || `evt-${idx}`;
                          const isExpanded = expandedEvents.has(eventId);
                          const isHeartbeat = evt.messageType === 'heartbeat';
                          
                          const eventCard = (
                              <Card 
                                size="small" 
                                className={`mb-2 cursor-pointer transition-all duration-200 ${isHeartbeat ? 'opacity-60' : ''} ${isExpanded ? 'shadow-md' : 'shadow-sm'}`}
                                onClick={() => !isHeartbeat && toggleEventExpansion(eventId)}
                              >
                              <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-2 flex-1">
                                    <Text strong className="text-sm">{getEventSummary(evt)}</Text>
                                    {!isHeartbeat && (
                                      <span className="text-xs text-gray-400">
                                        {isExpanded ? '▼' : '▶'}
                                      </span>
                                    )}
                                  </div>
                                <Text type="secondary" className="text-xs">{formatTime(evt.createTime || Date.now())}</Text>
                              </div>
                              {evt.taskId && (
                                <div className="mt-1 text-xs text-gray-500">任务: {evt.taskId}{evt.taskOrder != null ? ` #${evt.taskOrder}` : ''}</div>
                              )}
                                
                                {/* 详细内容 - 只在展开时显示 */}
                                {isExpanded && !isHeartbeat && evt.payloadJson && (() => {
                                try {
                                  const obj = JSON.parse(evt.payloadJson);
                                  
                                  // 处理eventData结构的详细信息
                                  if (obj.eventData) {
                                    const eventData = obj.eventData;
                                    const resultMap = eventData.resultMap || {};
                                    const messageOrder = eventData.messageOrder || 0;
                                    
                                    if (resultMap.messageType === 'deep_search') {
                                      const searchResult = resultMap.resultMap?.searchResult;
                                      const answer = resultMap.resultMap?.answer;
                                      const searchFinish = resultMap.resultMap?.searchFinish;
                                      
                                      return (
                                        <div className="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3">
                                          <div className="flex items-center mb-2">
                                            <span className="text-base">🔍</span>
                                            <Text strong className="ml-2 text-blue-700 text-sm">深度搜索 - 第 {messageOrder} 步</Text>
                                            <Badge 
                                              status={searchFinish ? 'success' : 'processing'} 
                                              text={searchFinish ? '搜索完成' : '搜索中'} 
                                              className="ml-2"
                                            />
                                          </div>
                                          
                                          {searchResult?.query && searchResult.query.length > 0 && (
                                            <div className="mb-3">
                                              <Text strong className="text-xs text-blue-600">搜索查询 ({searchResult.query.length} 个):</Text>
                                              <div className="mt-1 bg-white rounded border p-2">
                                                {searchResult.query.map((q: string, i: number) => (
                                                  <div key={i} className="text-xs text-gray-700 mb-1">• {q}</div>
                                                ))}
                                              </div>
                                            </div>
                                          )}
                                          
                                          {searchResult?.docs && (
                                            <div className="mb-3">
                                              <Text strong className="text-xs text-blue-600">
                                                搜索结果: {searchResult.docs.flat().length} 个相关文档
                                              </Text>
                                              <div className="mt-1 max-h-32 overflow-y-auto bg-white rounded border p-2">
                                                {searchResult.docs.map((group: any[], groupIndex: number) => (
                                                  group.length > 0 && (
                                                    <div key={groupIndex} className="mb-2">
                                                      <div className="text-xs text-gray-600 mb-1">第 {groupIndex + 1} 组 ({group.length} 个):</div>
                                                      {group.slice(0, 3).map((doc: any, docIndex: number) => (
                                                        <div key={docIndex} className="ml-2 mb-1 text-xs">
                                                          <Text strong className="text-gray-800">• {doc.title || '无标题'}</Text>
                                                          {doc.content && (
                                                            <div className="ml-2 text-gray-600 text-xs">
                                                              {doc.content.length > 80 ? 
                                                                `${doc.content.substring(0, 80)}...` : doc.content}
                                                            </div>
                                                          )}
                                                        </div>
                                                      ))}
                                                      {group.length > 3 && (
                                                        <div className="ml-2 text-xs text-gray-500">... 还有 {group.length - 3} 个文档</div>
                                                      )}
                                                    </div>
                                                  )
                                                ))}
                                              </div>
                                            </div>
                                          )}
                                          
                                          {answer && (
                                            <div>
                                              <Text strong className="text-xs text-blue-600">生成内容:</Text>
                                              <div className="mt-1 p-2 bg-white rounded border max-h-40 overflow-y-auto">
                                                <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words font-sans">
                                                  {answer.length > 600 ? `${answer.substring(0, 600)}...` : answer}
                                                </pre>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      );
                                      
                                    } else if (resultMap.messageType === 'result') {
                                      const taskSummary = resultMap.resultMap?.taskSummary || resultMap.result;
                                      const fileList = resultMap.resultMap?.fileList;
                                      
                                      return (
                                        <div className="mt-3 bg-green-50 border border-green-200 rounded-lg p-3">
                                          <div className="flex items-center mb-2">
                                            <span className="text-base">🎉</span>
                                            <Text strong className="ml-2 text-green-700 text-sm">任务完成 - 最终结果</Text>
                                          </div>
                                          
                                          {taskSummary && (
                                            <div className="mb-3">
                                              <Text strong className="text-xs text-green-600">任务摘要:</Text>
                                              <div className="mt-1 p-2 bg-white rounded border">
                                                <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words font-sans">
                                                  {taskSummary}
                                                </pre>
                                              </div>
                                            </div>
                                          )}
                                          
                                          {fileList && fileList.length > 0 && (
                                            <div>
                                              <Text strong className="text-xs text-green-600">
                                                生成的文件 ({fileList.length} 个):
                                              </Text>
                                              <div className="mt-1 space-y-1">
                                                {fileList.map((file: any, fileIndex: number) => (
                                                  <div key={fileIndex} className="p-2 bg-white rounded border">
                                                    <div className="flex items-center justify-between">
                                                      <div className="flex items-center">
                                                        <span className="text-xs">📄</span>
                                                        <Text strong className="ml-1 text-xs">{file.fileName}</Text>
                                                      </div>
                                                      <Text className="text-xs text-gray-500">{file.fileSize} 字节</Text>
                                                    </div>
                                                    {file.description && (
                                                      <div className="mt-1 text-xs text-gray-600">
                                                        {file.description.length > 150 ? 
                                                          `${file.description.substring(0, 150)}...` : 
                                                          file.description}
                                                      </div>
                                                    )}
                                                  </div>
                                                ))}
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      );
                                      
                                    } else if (resultMap.messageType === 'tool_thought') {
                                      return (
                                        <div className="mt-3 bg-purple-50 border border-purple-200 rounded-lg p-3">
                                          <div className="flex items-center mb-2">
                                            <span className="text-base">🤔</span>
                                            <Text strong className="ml-2 text-purple-700 text-sm">工具思考 - 第 {messageOrder} 步</Text>
                                          </div>
                                          {resultMap.task && (
                                            <div className="text-xs text-gray-700">
                                              <Text strong>思考内容:</Text> {resultMap.task}
                                            </div>
                                          )}
                                          {resultMap.plan && (
                                            <div className="mt-2 p-2 bg-white rounded border">
                                              <Text strong className="text-xs">计划:</Text>
                                              <pre className="mt-1 text-xs text-gray-600 whitespace-pre-wrap max-h-20 overflow-y-auto">
                                                {JSON.stringify(resultMap.plan, null, 2)}
                                              </pre>
                                            </div>
                                          )}
                                        </div>
                                      );
                                      
                                    } else {
                                      // 其他类型的事件，简化显示
                                      return (
                                        <div className="mt-2 bg-gray-50 border border-gray-200 rounded p-2">
                                          <div className="flex items-center mb-1">
                                            <span className="text-sm">📝</span>
                                            <Text strong className="ml-1 text-gray-700 text-xs">
                                              {resultMap.messageType || '未知类型'} - 第 {messageOrder} 步
                                            </Text>
                                          </div>
                                          {resultMap.task && (
                                            <div className="text-xs text-gray-600">内容: {resultMap.task}</div>
                                          )}
                                          {resultMap.result && (
                                            <div className="text-xs text-gray-600">结果: {resultMap.result}</div>
                                          )}
                                        </div>
                                      );
                                    }
                                  } else {
                                    // 兼容原有格式
                                  const mt = obj?.messageType || obj?.resultMap?.messageType;
                                  const rm = obj?.resultMap || obj;
                                    
                                    if (mt || rm?.task || rm?.result) {
                                      return (
                                        <div className="mt-2 p-2 bg-gray-50 rounded border">
                                          <pre className="text-xs text-gray-600 whitespace-pre-wrap break-words">
                                            {JSON.stringify({ messageType: mt, task: rm?.task, result: rm?.result }, null, 2)}
                                          </pre>
                                        </div>
                                      );
                                    }
                                  }
                                } catch (e) {
                                  // JSON解析失败时显示原始内容
                                  const trimmed = evt.payloadJson.trim();
                                  if (trimmed && trimmed !== '{}' && trimmed !== 'null') {
                                    return (
                                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                                        <Text strong className="text-xs text-yellow-700">原始数据:</Text>
                                        <pre className="mt-1 text-xs text-gray-600 whitespace-pre-wrap break-words">
                                          {trimmed}
                                        </pre>
                                      </div>
                                    );
                                  }
                                }
                                
                                return null;
                              })()}
                              </Card>
                          );
                          
                          return (
                            <div key={eventId} className="flex">
                              <div className="mr-4 flex flex-col items-center">
                                <Badge status={evt.isFinal ? 'success' : 'processing'} />
                                {idx < filteredEvents.length - 1 && (
                                  <div className="w-px bg-gray-200 h-8 mt-2"></div>
                                )}
                              </div>
                              {eventCard}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Col>
            
            {/* 侧边栏 - 文件列表 */}
            <Col span={8} className="h-full">
              <div className="bg-white rounded-lg shadow-sm m-4 h-[calc(100%-2rem)] overflow-auto">
                <div className="p-6">
                  <Title level={4} className="mb-4">生成文件</Title>
                  {files.length === 0 ? (
                    <Empty description="暂无生成文件" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  ) : (
                    <List
                      dataSource={files}
                      renderItem={(file) => (
                        <List.Item
                          actions={[
                            <Tooltip title="下载文件">
                              <Button
                                type="text"
                                icon={<DownloadOutlined />}
                                onClick={() => handleDownloadFile(file)}
                                size="small"
                              />
                            </Tooltip>,
                          ]}
                        >
                          <List.Item.Meta
                            avatar={<Avatar icon={<FileTextOutlined />} />}
                            title={
                              <div>
                                <Text strong className="text-sm">{file.fileName}</Text>
                                <br />
                                <Text type="secondary" className="text-xs">
                                  {(file.fileSize / 1024).toFixed(1)} KB
                                </Text>
                              </div>
                            }
                            description={
                              <div>
                                <Tag>{file.fileType}</Tag>
                                <br />
                                <Text type="secondary" className="text-xs">
                                  {formatTime(file.createTime)}
                                </Text>
                              </div>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  )}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
});

SessionDetail.displayName = 'SessionDetail';

export default SessionDetail;