// 会话详情页面 - 历史记录查看
// 新增功能：查看历史会话的详细信息，包括消息记录、文件列表、执行过程等

import React, { memo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Spin,
  Alert,
  Space,
  Breadcrumb,
  message,
} from 'antd';
import {
  ArrowLeftOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { useSessionDetail } from '@/hooks/useHistory';
import { formatTime } from '@/utils';
import HistoryReplay from '@/components/HistoryReplay';
import { validateHistoryData, getHistoryStats } from '@/utils/historyReplay';
// 引入数据验证工具用于生产环境验证
import '@/utils/dataValidation';

const { Title } = Typography;

type SessionDetailProps = Record<string, never>;

const SessionDetail: React.FC<SessionDetailProps> = memo(() => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();

  // 使用历史记录详情hook
  const {
    session,
    messages,
    files,
    events,
    loading,
  } = useSessionDetail(sessionId || '');

  // 数据验证和统计（开发环境）
  const historyValidation = validateHistoryData(events, messages, files);
  const historyStats = getHistoryStats(events, messages, files);
  
  // 在开发环境下输出验证结果
  if (process.env.NODE_ENV === 'development') {
    if (!historyValidation.isValid) {
      console.warn('History data validation issues:', historyValidation.issues);
    }
    console.log('History stats:', historyStats);
  }

  // 返回历史记录列表
  const handleBack = useCallback(() => {
    navigate('/history');
  }, [navigate]);

  // 如果没有sessionId，显示错误
  if (!sessionId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Alert
          message="错误"
          description="缺少会话ID参数"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleBack}>
              返回列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={handleBack}
                className="flex items-center"
              >
                返回
              </Button>
              
              <Breadcrumb
                items={[
                  { title: '历史记录' },
                  { title: session?.title || '会话详情' },
                ]}
              />
            </div>

            {loading && (
              <div className="flex items-center space-x-2 text-gray-500">
                <LoadingOutlined />
                <span>加载中...</span>
              </div>
            )}
          </div>

          {/* 会话基本信息 */}
          {session && (
            <div className="mt-4">
              <div className="flex items-center justify-between">
                <div>
                  <Title level={3} className="mb-2">
                    {session.title}
                  </Title>
                  <Space className="text-sm text-gray-500">
                    <span>创建时间：{formatTime(session.createTime)}</span>
                    <span>•</span>
                    <span>更新时间：{formatTime(session.updateTime)}</span>
                    <span>•</span>
                    <span>消息数：{messages.length}</span>
                    <span>•</span>
                    <span>事件数：{events.length}</span>
                    <span>•</span>
                    <span>文件数：{files.length}</span>
                  </Space>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="max-w-7xl mx-auto h-full">
          <div className="bg-white rounded-lg shadow-sm m-4 h-[calc(100vh-200px)]">
            {/* 使用新的历史回放组件 */}
            <HistoryReplay
              session={session}
              messages={messages}
              events={events}
              files={files}
              loading={loading}
            />
          </div>
        </div>
      </div>
    </div>
  );
});

SessionDetail.displayName = 'SessionDetail';

export default SessionDetail;
