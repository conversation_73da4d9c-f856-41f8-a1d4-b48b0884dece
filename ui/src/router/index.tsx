import React, { Suspense } from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import Layout from '@/layout/index';
import { Loading } from '@/components';

// 使用常量存储路由路径
const ROUTES = {
  HOME: '/',
  HISTORY: '/history', // 新增：历史记录路由
  SESSION_DETAIL: '/history/session/:sessionId', // 新增：会话详情路由
  NOT_FOUND: '*',
};

// 使用 React.lazy 懒加载组件
const Home = React.lazy(() => import('@/pages/Home'));
const History = React.lazy(() => import('@/pages/History')); // 新增：历史记录页面
const SessionDetail = React.lazy(() => import('@/pages/SessionDetail')); // 新增：会话详情页面
const NotFound = React.lazy(() => import('@/components/NotFound'));

// 创建路由配置
const router = createBrowserRouter([
  {
    path: ROUTES.HOME,
    element: <Layout />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<Loading loading={true} className="h-full"/>}>
            <Home />
          </Suspense>
        ),
      },
      {
        path: ROUTES.HISTORY,
        element: (
          <Suspense fallback={<Loading loading={true} className="h-full"/>}>
            <History />
          </Suspense>
        ),
      },
      {
        path: ROUTES.SESSION_DETAIL,
        element: (
          <Suspense fallback={<Loading loading={true} className="h-full"/>}>
            <SessionDetail />
          </Suspense>
        ),
      },
      {
        path: ROUTES.NOT_FOUND,
        element: (
          <Suspense fallback={<Loading loading={true} className="h-full"/>}>
            <NotFound />
          </Suspense>
        ),
      },
    ],
  },
  // 重定向所有未匹配的路由到 404 页面
  {
    path: '*',
    element: <Navigate to={ROUTES.NOT_FOUND} replace />,
  },
]);

export default router;
