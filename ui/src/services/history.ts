import api from './index';

export const historyApi = {
  listSessions: (params?: {
    keyword?: string;
    status?: string;
    start?: number;
    end?: number;
    tags?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC' | 'asc' | 'desc';
    page?: number;
    pageSize?: number;
  }) => api.get<HISTORY.SessionHistory[]>(`/web/api/v1/history/sessions`, params),

  getSessionDetail: (sessionId: string) => api.get<{session: any; messages: any[]; files: any[]}>(`/web/api/v1/history/sessions/${sessionId}`),

  getSessionEvents: (sessionId: string) => api.get<any[]>(`/web/api/v1/history/sessions/${sessionId}/events`),

  importHistory: (payload: any) => api.post<boolean>(`/web/api/v1/history/import`, payload),

  // 数据验证相关API
  validateSession: (sessionId: string) => api.get<any>(`/web/api/v1/history/sessions/${sessionId}/validate`),

  validateMultipleSessions: (sessionIds: string[]) => api.post<Record<string, any>>(`/web/api/v1/history/validate/batch`, { sessionIds }),
};

export default historyApi;

