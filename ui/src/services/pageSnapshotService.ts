/**
 * 页面快照保存服务
 * 负责快照的保存、加载和管理
 */

import { PAGE_SNAPSHOT } from '@/types/pageSnapshot';
import api from './index';

const STORAGE_KEYS = {
  SNAPSHOTS: 'genie_page_snapshots',
  CONFIG: 'genie_snapshot_config',
  CACHE: 'genie_snapshot_cache'
};

/**
 * 默认快照配置
 */
const DEFAULT_CONFIG: PAGE_SNAPSHOT.SnapshotConfig = {
  autoSave: true,
  saveOnTaskComplete: true,
  showSaveConfirmation: true,
  maxSnapshots: 50
};

/**
 * 页面快照服务类
 */
export class PageSnapshotService {
  private config: PAGE_SNAPSHOT.SnapshotConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  /**
   * 保存页面快照
   */
  async saveSnapshot(snapshot: PAGE_SNAPSHOT.CompletePageSnapshot): Promise<PAGE_SNAPSHOT.SnapshotSaveResult> {
    const result: PAGE_SNAPSHOT.SnapshotSaveResult = {
      success: false,
      savedTo: []
    };

    try {
      // 1. 保存到后端数据库
      try {
        const response = await api.post('/web/api/v1/snapshots', {
          snapshot: snapshot
        });
        
        if (response.data.code === 200) {
          result.savedTo.push('database');
          result.snapshotId = snapshot.id;
        }
      } catch (dbError) {
        console.warn('Failed to save snapshot to database:', dbError);
      }

      // 2. 保存到localStorage作为备份
      try {
        this.saveToLocalStorage(snapshot);
        result.savedTo.push('localStorage');
      } catch (localError) {
        console.warn('Failed to save snapshot to localStorage:', localError);
      }

      // 3. 判断保存是否成功
      result.success = result.savedTo.length > 0;
      
      if (!result.success) {
        result.error = '快照保存失败：数据库和本地存储都不可用';
      }

      // 4. 触发保存事件
      if (result.success) {
        this.emitSnapshotSaveEvent(snapshot);
      }

      return result;

    } catch (error) {
      return {
        success: false,
        error: `快照保存异常: ${error}`,
        savedTo: []
      };
    }
  }

  /**
   * 加载页面快照
   */
  async loadSnapshot(snapshotId: string): Promise<PAGE_SNAPSHOT.CompletePageSnapshot | null> {
    try {
      // 1. 优先从数据库加载
      try {
        const response = await api.get(`/web/api/v1/snapshots/${snapshotId}`);
        if (response.data.code === 200 && response.data.data) {
          this.emitSnapshotLoadEvent(snapshotId);
          return response.data.data.snapshot;
        }
      } catch (dbError) {
        console.warn('Failed to load snapshot from database:', dbError);
      }

      // 2. 从localStorage加载
      const localSnapshots = this.getLocalSnapshots();
      const snapshot = localSnapshots.find(s => s.id === snapshotId);
      
      if (snapshot) {
        this.emitSnapshotLoadEvent(snapshotId);
        return snapshot;
      }

      return null;

    } catch (error) {
      console.error('Failed to load snapshot:', error);
      return null;
    }
  }

  /**
   * 获取会话的快照列表
   */
  async getSessionSnapshots(sessionId: string): Promise<PAGE_SNAPSHOT.SnapshotListItem[]> {
    try {
      // 1. 从数据库获取
      try {
        const response = await api.get(`/web/api/v1/snapshots/session/${sessionId}`);
        if (response.data.code === 200 && response.data.data) {
          return response.data.data.snapshots;
        }
      } catch (dbError) {
        console.warn('Failed to get snapshots from database:', dbError);
      }

      // 2. 从localStorage获取
      const localSnapshots = this.getLocalSnapshots();
      return localSnapshots
        .filter(snapshot => snapshot.sessionId === sessionId)
        .map(snapshot => this.convertToListItem(snapshot))
        .sort((a, b) => b.timestamp - a.timestamp);

    } catch (error) {
      console.error('Failed to get session snapshots:', error);
      return [];
    }
  }

  /**
   * 删除快照
   */
  async deleteSnapshot(snapshotId: string): Promise<boolean> {
    try {
      // 1. 从数据库删除
      try {
        await api.delete(`/web/api/v1/snapshots/${snapshotId}`);
      } catch (dbError) {
        console.warn('Failed to delete snapshot from database:', dbError);
      }

      // 2. 从localStorage删除
      const localSnapshots = this.getLocalSnapshots();
      const filteredSnapshots = localSnapshots.filter(s => s.id !== snapshotId);
      this.saveLocalSnapshots(filteredSnapshots);

      return true;

    } catch (error) {
      console.error('Failed to delete snapshot:', error);
      return false;
    }
  }

  /**
   * 获取快照配置
   */
  getConfig(): PAGE_SNAPSHOT.SnapshotConfig {
    return { ...this.config };
  }

  /**
   * 更新快照配置
   */
  updateConfig(newConfig: Partial<PAGE_SNAPSHOT.SnapshotConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
  }

  /**
   * 清理过期快照
   */
  async cleanupExpiredSnapshots(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<number> {
    const expireTime = Date.now() - maxAge;
    let cleanedCount = 0;

    try {
      // 1. 清理localStorage中的过期快照
      const localSnapshots = this.getLocalSnapshots();
      const validSnapshots = localSnapshots.filter(snapshot => {
        if (snapshot.timestamp < expireTime) {
          cleanedCount++;
          return false;
        }
        return true;
      });
      
      this.saveLocalSnapshots(validSnapshots);

      // 2. 通知后端清理过期快照
      try {
        await api.post('/web/api/v1/snapshots/cleanup', {
          maxAge: maxAge
        });
      } catch (error) {
        console.warn('Failed to cleanup database snapshots:', error);
      }

      return cleanedCount;

    } catch (error) {
      console.error('Failed to cleanup expired snapshots:', error);
      return 0;
    }
  }

  /**
   * 保存到localStorage
   */
  private saveToLocalStorage(snapshot: PAGE_SNAPSHOT.CompletePageSnapshot): void {
    const snapshots = this.getLocalSnapshots();
    
    // 添加新快照
    snapshots.unshift(snapshot);
    
    // 限制数量
    if (snapshots.length > this.config.maxSnapshots) {
      snapshots.splice(this.config.maxSnapshots);
    }
    
    this.saveLocalSnapshots(snapshots);
  }

  /**
   * 从localStorage获取快照
   */
  private getLocalSnapshots(): PAGE_SNAPSHOT.CompletePageSnapshot[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.SNAPSHOTS);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to parse local snapshots:', error);
      return [];
    }
  }

  /**
   * 保存快照到localStorage
   */
  private saveLocalSnapshots(snapshots: PAGE_SNAPSHOT.CompletePageSnapshot[]): void {
    try {
      localStorage.setItem(STORAGE_KEYS.SNAPSHOTS, JSON.stringify(snapshots));
    } catch (error) {
      console.error('Failed to save snapshots to localStorage:', error);
    }
  }

  /**
   * 加载配置
   */
  private loadConfig(): PAGE_SNAPSHOT.SnapshotConfig {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.CONFIG);
      return data ? { ...DEFAULT_CONFIG, ...JSON.parse(data) } : DEFAULT_CONFIG;
    } catch (error) {
      console.error('Failed to load snapshot config:', error);
      return DEFAULT_CONFIG;
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem(STORAGE_KEYS.CONFIG, JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save snapshot config:', error);
    }
  }

  /**
   * 转换为列表项格式
   */
  private convertToListItem(snapshot: PAGE_SNAPSHOT.CompletePageSnapshot): PAGE_SNAPSHOT.SnapshotListItem {
    return {
      id: snapshot.id,
      sessionId: snapshot.sessionId,
      timestamp: snapshot.timestamp,
      type: snapshot.type,
      isFinal: snapshot.isFinal,
      taskStatus: snapshot.taskStatus,
      activeView: snapshot.activeView,
      preview: {
        query: snapshot.chatInfo.query.slice(0, 100) + (snapshot.chatInfo.query.length > 100 ? '...' : ''),
        fileCount: snapshot.filePage.fileList.length,
        searchCount: snapshot.browserPage.searchHistory.length,
        taskCount: snapshot.followPage.taskList.length
      }
    };
  }

  /**
   * 触发快照保存事件
   */
  private emitSnapshotSaveEvent(snapshot: PAGE_SNAPSHOT.CompletePageSnapshot): void {
    const event = new CustomEvent('snapshotSaved', {
      detail: {
        snapshotId: snapshot.id,
        sessionId: snapshot.sessionId,
        timestamp: snapshot.timestamp,
        saveReason: snapshot.metadata.saveReason
      }
    });
    window.dispatchEvent(event);
  }

  /**
   * 触发快照加载事件
   */
  private emitSnapshotLoadEvent(snapshotId: string): void {
    const event = new CustomEvent('snapshotLoaded', {
      detail: {
        snapshotId: snapshotId,
        loadTime: Date.now()
      }
    });
    window.dispatchEvent(event);
  }
}

// 创建单例实例
export const pageSnapshotService = new PageSnapshotService();

// 导出便捷方法
export const {
  saveSnapshot,
  loadSnapshot,
  getSessionSnapshots,
  deleteSnapshot,
  getConfig,
  updateConfig,
  cleanupExpiredSnapshots
} = pageSnapshotService;
