// 历史记录相关类型定义
// 新增功能：历史记录查看页面的类型定义

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace HISTORY {
    // 会话历史记录
    export type SessionHistory = {
      id: string;
      sessionId: string;
      title: string;
      createTime: number;
      updateTime: number;
      messageCount: number;
      status: 'completed' | 'running' | 'error';
      tags: string[];
      summary?: string;
      product?: CHAT.Product;
    };

    // 消息历史记录
    export type MessageHistory = {
      id: string;
      sessionId: string;
      type: 'user' | 'assistant' | 'system';
      content: string;
      timestamp: number;
      files?: CHAT.TFile[];
      tasks?: CHAT.Task[][];
      plan?: CHAT.Plan;
      thought?: string;
      toolThought?: string;
      planThought?: string;
      response?: string;
      taskStatus?: MESSAGE.MsgItem['taskStatus'];
      toolResult?: MESSAGE.ToolResult;
      resultMap?: MESSAGE.ResultMap;
    };

    // 文件历史记录
    export type FileHistory = {
      id: string;
      sessionId: string;
      messageId: string;
      fileName: string;
      filePath: string;
      fileType: string;
      fileSize: number;
      createTime: number;
      description?: string;
      downloadUrl?: string;
    };

    // 搜索筛选条件
    export type SearchFilter = {
      keyword?: string;
      dateRange?: [number, number];
      status?: SessionHistory['status'][];
      tags?: string[];
      productType?: string;
      sortBy?: 'createTime' | 'updateTime' | 'messageCount';
      sortOrder?: 'asc' | 'desc';
    };

    // 历史记录统计
    export type HistoryStats = {
      totalSessions: number;
      totalMessages: number;
      totalFiles: number;
      recentActivity: number;
    };

    // 导出数据格式
    export type ExportData = {
      sessions: SessionHistory[];
      messages: MessageHistory[];
      files: FileHistory[];
      exportTime: number;
      version: string;
    };

    // 分页参数
    export type PaginationParams = {
      page: number;
      pageSize: number;
      total?: number;
    };

    // 历史记录列表响应
    export type HistoryListResponse = {
      sessions: SessionHistory[];
      pagination: PaginationParams;
      stats?: HistoryStats;
    };

    // 会话详情响应
    export type SessionDetailResponse = {
      session: SessionHistory;
      messages: MessageHistory[];
      files: FileHistory[];
    };
  }
}

export {};