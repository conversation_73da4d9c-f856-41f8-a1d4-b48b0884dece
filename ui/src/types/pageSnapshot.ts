/**
 * 页面状态快照类型定义
 * 用于保存任务完成时的完整页面状态
 */

export namespace PAGE_SNAPSHOT {
  /**
   * 实时跟踪页面状态
   */
  export interface FollowPageState {
    /** 当前活跃的任务 */
    activeTask?: CHAT.Task;
    /** 任务列表 */
    taskList: CHAT.Task[];
    /** 当前任务索引 */
    currentTaskIndex: number;
    /** 滑块位置 */
    sliderValue: number;
    /** 任务执行时序 */
    taskTimeline: Array<{
      taskId: string;
      messageType: string;
      timestamp: number;
      content: any;
    }>;
  }

  /**
   * 浏览器页面状态
   */
  export interface BrowserPageState {
    /** 搜索记录列表 */
    searchHistory: Array<{
      id: string;
      query: string;
      timestamp: number;
      resultCount: number;
      results: Array<{
        name: string;
        url: string;
        pageContent?: string;
      }>;
    }>;
    /** 当前选中的搜索项 */
    activeSearchId?: string;
  }

  /**
   * 文件页面状态
   */
  export interface FilePageState {
    /** 文件列表 */
    fileList: Array<{
      id: string;
      name: string;
      type: string;
      url: string;
      downloadUrl?: string;
      size?: number;
      timestamp: number;
      taskId?: string;
      content?: string; // 文件内容预览
    }>;
    /** 当前选中的文件 */
    activeFileId?: string;
  }

  /**
   * 完整的页面状态快照
   */
  export interface CompletePageSnapshot {
    /** 快照ID */
    id: string;
    /** 会话ID */
    sessionId: string;
    /** 快照创建时间 */
    timestamp: number;
    /** 快照类型 */
    type: 'auto' | 'manual';
    /** 任务状态 */
    taskStatus: number;
    /** 是否为最终状态 */
    isFinal: boolean;
    
    /** ActionView当前活跃的标签页 */
    activeView: 'follow' | 'browser' | 'file';
    
    /** 三个标签页的状态 */
    followPage: FollowPageState;
    browserPage: BrowserPageState;
    filePage: FilePageState;
    
    /** 聊天基础信息 */
    chatInfo: {
      query: string;
      response?: string;
      thought?: string;
      plan?: CHAT.Plan;
    };
    
    /** 元数据 */
    metadata: {
      userAgent: string;
      screenResolution: string;
      saveReason: 'task_completed' | 'user_manual' | 'auto_interval';
    };
  }

  /**
   * 快照保存配置
   */
  export interface SnapshotConfig {
    /** 是否启用自动保存 */
    autoSave: boolean;
    /** 是否在任务完成时保存 */
    saveOnTaskComplete: boolean;
    /** 是否显示保存确认 */
    showSaveConfirmation: boolean;
    /** 最大保存数量 */
    maxSnapshots: number;
  }

  /**
   * 快照保存结果
   */
  export interface SnapshotSaveResult {
    success: boolean;
    snapshotId?: string;
    error?: string;
    savedTo: Array<'database' | 'localStorage'>;
  }

  /**
   * 快照查询参数
   */
  export interface SnapshotQuery {
    sessionId: string;
    type?: 'auto' | 'manual';
    startTime?: number;
    endTime?: number;
    isFinal?: boolean;
  }

  /**
   * 快照列表项
   */
  export interface SnapshotListItem {
    id: string;
    sessionId: string;
    timestamp: number;
    type: 'auto' | 'manual';
    isFinal: boolean;
    taskStatus: number;
    activeView: 'follow' | 'browser' | 'file';
    preview: {
      query: string;
      fileCount: number;
      searchCount: number;
      taskCount: number;
    };
  }
}

/**
 * 快照保存事件类型
 */
export type SnapshotSaveEvent = {
  type: 'snapshot_saved';
  payload: {
    snapshotId: string;
    sessionId: string;
    timestamp: number;
    saveReason: string;
  };
};

/**
 * 快照加载事件类型
 */
export type SnapshotLoadEvent = {
  type: 'snapshot_loaded';
  payload: {
    snapshotId: string;
    sessionId: string;
    loadTime: number;
  };
};
