/**
 * 数据一致性工具
 * 确保实时数据和历史回放数据的格式一致性
 */

import { normalizeEventData } from './dataTransform';

/**
 * 统一的事件数据格式化器
 */
export interface StandardEventData {
  id: string;
  sessionId: string;
  messageId: string;
  messageType: string;
  messageOrder: number;
  taskId?: string;
  taskOrder: number;
  isFinal: boolean;
  createTime: number;
  payload: any;
  // 扩展字段
  planThought?: string;
  toolThought?: string;
  task?: any;
  result?: any;
  toolResult?: any;
  taskSummary?: any;
  resultMap?: any;
}

/**
 * 标准化事件数据 - 统一实时和历史数据格式
 */
export function standardizeEventData(rawEvent: any, isRealtime: boolean = false): StandardEventData {
  // 基础数据标准化
  const normalized = normalizeEventData(rawEvent);
  
  let payload = normalized.payload;
  let eventData: any = {};
  
  if (isRealtime) {
    // 实时数据处理
    eventData = rawEvent.resultMap?.eventData || rawEvent.eventData || {};
    payload = rawEvent.resultMap || rawEvent;
  } else {
    // 历史数据处理
    if (payload?.eventData) {
      eventData = payload.eventData;
    } else {
      eventData = payload || {};
    }
  }
  
  return {
    id: normalized.id || `${normalized.messageId}_${normalized.createTime}`,
    sessionId: normalized.sessionId,
    messageId: normalized.messageId || eventData.messageId,
    messageType: normalized.messageType || eventData.messageType,
    messageOrder: normalized.messageOrder || eventData.messageOrder || 0,
    taskId: normalized.taskId || eventData.taskId,
    taskOrder: normalized.taskOrder || eventData.taskOrder || 0,
    isFinal: normalized.isFinal || eventData.isFinal || false,
    createTime: normalized.createTime,
    payload: payload,
    // 扩展字段
    planThought: eventData.planThought,
    toolThought: eventData.toolThought,
    task: eventData.task,
    result: eventData.result,
    toolResult: eventData.toolResult,
    taskSummary: eventData.taskSummary,
    resultMap: eventData.resultMap || {},
  };
}

/**
 * 验证事件数据完整性
 */
export function validateEventData(eventData: StandardEventData): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} {
  const missingFields: string[] = [];
  const warnings: string[] = [];
  
  // 必需字段检查
  if (!eventData.sessionId) missingFields.push('sessionId');
  if (!eventData.messageType) missingFields.push('messageType');
  if (!eventData.createTime) missingFields.push('createTime');
  
  // 警告检查
  if (!eventData.messageId) warnings.push('messageId missing - may affect event tracking');
  if (eventData.messageOrder === undefined) warnings.push('messageOrder missing - may affect sequence');
  if (!eventData.payload) warnings.push('payload empty - may affect data reconstruction');
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings,
  };
}

/**
 * 时间戳格式化工具
 */
export function formatEventTimestamp(timestamp: number, format: 'relative' | 'absolute' | 'time' = 'relative'): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffTime = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  switch (format) {
    case 'absolute':
      return date.toLocaleString('zh-CN');
    case 'time':
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    case 'relative':
    default:
      if (diffDays === 0) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else if (diffDays === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
      }
  }
}

/**
 * 事件序列排序工具
 */
export function sortEventsBySequence(events: StandardEventData[]): StandardEventData[] {
  return [...events].sort((a, b) => {
    // 首先按时间排序
    const timeDiff = a.createTime - b.createTime;
    if (timeDiff !== 0) return timeDiff;
    
    // 时间相同时按消息顺序排序
    const orderDiff = a.messageOrder - b.messageOrder;
    if (orderDiff !== 0) return orderDiff;
    
    // 最后按任务顺序排序
    return a.taskOrder - b.taskOrder;
  });
}

/**
 * 数据一致性检查器
 */
export function checkDataConsistency(realtimeData: any[], historyData: StandardEventData[]): {
  consistent: boolean;
  differences: Array<{
    field: string;
    realtimeValue: any;
    historyValue: any;
    eventId: string;
  }>;
  summary: {
    totalEvents: number;
    consistentEvents: number;
    inconsistentEvents: number;
  };
} {
  const differences: Array<{
    field: string;
    realtimeValue: any;
    historyValue: any;
    eventId: string;
  }> = [];
  
  let consistentEvents = 0;
  let inconsistentEvents = 0;
  
  // 创建实时数据映射
  const realtimeMap = new Map();
  realtimeData.forEach(event => {
    const standardized = standardizeEventData(event, true);
    realtimeMap.set(standardized.messageId, standardized);
  });
  
  // 比较历史数据
  historyData.forEach(historyEvent => {
    const realtimeEvent = realtimeMap.get(historyEvent.messageId);
    if (!realtimeEvent) {
      inconsistentEvents++;
      return;
    }
    
    let eventConsistent = true;
    const fieldsToCheck = ['messageType', 'taskId', 'isFinal', 'messageOrder', 'taskOrder'];
    
    fieldsToCheck.forEach(field => {
      if (realtimeEvent[field] !== historyEvent[field]) {
        differences.push({
          field,
          realtimeValue: realtimeEvent[field],
          historyValue: historyEvent[field],
          eventId: historyEvent.id,
        });
        eventConsistent = false;
      }
    });
    
    if (eventConsistent) {
      consistentEvents++;
    } else {
      inconsistentEvents++;
    }
  });
  
  return {
    consistent: differences.length === 0,
    differences,
    summary: {
      totalEvents: historyData.length,
      consistentEvents,
      inconsistentEvents,
    },
  };
}

/**
 * 数据修复工具
 */
export function repairEventData(eventData: StandardEventData): StandardEventData {
  const repaired = { ...eventData };
  
  // 修复缺失的ID
  if (!repaired.id) {
    repaired.id = `${repaired.messageId || 'unknown'}_${repaired.createTime}`;
  }
  
  // 修复缺失的时间戳
  if (!repaired.createTime) {
    repaired.createTime = Date.now();
  }
  
  // 修复缺失的顺序
  if (repaired.messageOrder === undefined) {
    repaired.messageOrder = 0;
  }
  
  if (repaired.taskOrder === undefined) {
    repaired.taskOrder = 0;
  }
  
  // 修复缺失的标志
  if (repaired.isFinal === undefined) {
    repaired.isFinal = false;
  }
  
  return repaired;
}
