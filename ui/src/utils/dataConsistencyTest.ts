/**
 * 数据一致性测试工具
 * 用于验证实时数据和历史回放数据的一致性
 */

import { standardizeEventData, checkDataConsistency, validateEventData } from './dataConsistency';
import { buildReplayChat } from './historyReplay';

/**
 * 测试数据一致性
 */
export function testDataConsistency(sessionId: string): Promise<{
  success: boolean;
  report: {
    totalEvents: number;
    consistentEvents: number;
    inconsistentEvents: number;
    differences: Array<{
      field: string;
      realtimeValue: any;
      historyValue: any;
      eventId: string;
    }>;
    validationErrors: Array<{
      eventId: string;
      missingFields: string[];
      warnings: string[];
    }>;
  };
  recommendations: string[];
}> {
  return new Promise(async (resolve) => {
    try {
      // 获取历史数据
      const historyResponse = await fetch(`/api/history/sessions/${sessionId}/events`);
      const historyData = await historyResponse.json();
      
      if (!historyData.success || !historyData.data) {
        resolve({
          success: false,
          report: {
            totalEvents: 0,
            consistentEvents: 0,
            inconsistentEvents: 0,
            differences: [],
            validationErrors: [],
          },
          recommendations: ['无法获取历史数据，请检查API接口'],
        });
        return;
      }
      
      const events = historyData.data.events || [];
      const messages = historyData.data.messages || [];
      
      // 标准化历史事件数据
      const standardizedEvents = events.map((evt: any) => {
        const standardized = standardizeEventData(evt, false);
        return standardized;
      });
      
      // 验证数据完整性
      const validationErrors: Array<{
        eventId: string;
        missingFields: string[];
        warnings: string[];
      }> = [];
      
      standardizedEvents.forEach((evt: any) => {
        const validation = validateEventData(evt);
        if (!validation.isValid || validation.warnings.length > 0) {
          validationErrors.push({
            eventId: evt.id,
            missingFields: validation.missingFields,
            warnings: validation.warnings,
          });
        }
      });
      
      // 尝试重建聊天数据
      const replayChat = buildReplayChat(events, messages, sessionId);
      
      // 生成报告
      const report = {
        totalEvents: events.length,
        consistentEvents: events.length - validationErrors.length,
        inconsistentEvents: validationErrors.length,
        differences: [], // 这里可以扩展为与实时数据的比较
        validationErrors,
      };
      
      // 生成建议
      const recommendations: string[] = [];
      
      if (validationErrors.length > 0) {
        recommendations.push(`发现 ${validationErrors.length} 个事件数据验证错误，建议检查数据保存逻辑`);
      }
      
      if (report.totalEvents === 0) {
        recommendations.push('没有找到事件数据，可能是数据保存功能未正常工作');
      }
      
      if (!replayChat) {
        recommendations.push('无法重建聊天数据，建议检查历史回放逻辑');
      }
      
      if (recommendations.length === 0) {
        recommendations.push('数据一致性检查通过，实时数据和历史数据格式一致');
      }
      
      resolve({
        success: true,
        report,
        recommendations,
      });
      
    } catch (error) {
      resolve({
        success: false,
        report: {
          totalEvents: 0,
          consistentEvents: 0,
          inconsistentEvents: 0,
          differences: [],
          validationErrors: [],
        },
        recommendations: [`测试过程中发生错误: ${error}`],
      });
    }
  });
}

/**
 * 生成数据一致性报告
 */
export function generateConsistencyReport(testResult: any): string {
  const { success, report, recommendations } = testResult;
  
  if (!success) {
    return `
数据一致性测试失败
===================

建议：
${recommendations.map(r => `• ${r}`).join('\n')}
    `;
  }
  
  const consistencyRate = report.totalEvents > 0 
    ? ((report.consistentEvents / report.totalEvents) * 100).toFixed(2)
    : '0';
  
  return `
数据一致性测试报告
==================

总体统计：
• 总事件数: ${report.totalEvents}
• 一致事件数: ${report.consistentEvents}
• 不一致事件数: ${report.inconsistentEvents}
• 一致性率: ${consistencyRate}%

验证错误详情：
${report.validationErrors.length > 0 
  ? report.validationErrors.map(err => 
      `• 事件 ${err.eventId}:\n  - 缺失字段: ${err.missingFields.join(', ')}\n  - 警告: ${err.warnings.join(', ')}`
    ).join('\n')
  : '• 无验证错误'
}

数据差异详情：
${report.differences.length > 0
  ? report.differences.map(diff =>
      `• 字段 ${diff.field} 在事件 ${diff.eventId} 中不一致:\n  - 实时值: ${diff.realtimeValue}\n  - 历史值: ${diff.historyValue}`
    ).join('\n')
  : '• 无数据差异'
}

建议：
${recommendations.map(r => `• ${r}`).join('\n')}
  `;
}

/**
 * 在控制台运行数据一致性测试
 */
export async function runConsistencyTest(sessionId: string): Promise<void> {
  console.log('开始数据一致性测试...');
  console.log('会话ID:', sessionId);
  
  const result = await testDataConsistency(sessionId);
  const report = generateConsistencyReport(result);
  
  console.log(report);
  
  // 如果有问题，提供调试信息
  if (!result.success || result.report.inconsistentEvents > 0) {
    console.group('调试信息');
    console.log('测试结果详情:', result);
    console.groupEnd();
  }
}

/**
 * 批量测试多个会话的数据一致性
 */
export async function batchConsistencyTest(sessionIds: string[]): Promise<{
  totalSessions: number;
  passedSessions: number;
  failedSessions: number;
  results: Array<{
    sessionId: string;
    success: boolean;
    consistencyRate: number;
    issues: number;
  }>;
}> {
  const results = [];
  let passedSessions = 0;
  let failedSessions = 0;
  
  for (const sessionId of sessionIds) {
    try {
      const testResult = await testDataConsistency(sessionId);
      const consistencyRate = testResult.report.totalEvents > 0
        ? (testResult.report.consistentEvents / testResult.report.totalEvents) * 100
        : 0;
      
      const issues = testResult.report.inconsistentEvents + testResult.report.validationErrors.length;
      
      results.push({
        sessionId,
        success: testResult.success && issues === 0,
        consistencyRate,
        issues,
      });
      
      if (testResult.success && issues === 0) {
        passedSessions++;
      } else {
        failedSessions++;
      }
    } catch (error) {
      results.push({
        sessionId,
        success: false,
        consistencyRate: 0,
        issues: 1,
      });
      failedSessions++;
    }
  }
  
  return {
    totalSessions: sessionIds.length,
    passedSessions,
    failedSessions,
    results,
  };
}
