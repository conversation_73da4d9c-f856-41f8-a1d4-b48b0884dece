// 数据转换工具 - 统一处理前后端数据格式差异

/**
 * 标准化消息数据结构
 * 处理后端 role -> 前端 type 的转换
 * 处理 JSON 字符串的解析
 */
export const normalizeMessageData = (rawMessage: any): HISTORY.MessageHistory => {
  const safeParseJson = (jsonString: string | null | undefined) => {
    if (!jsonString || typeof jsonString !== 'string') return undefined;
    try {
      return JSON.parse(jsonString);
    } catch {
      return undefined;
    }
  };

  return {
    id: rawMessage.id || `msg-${Date.now()}-${Math.random()}`,
    sessionId: rawMessage.sessionId,
    type: (rawMessage.role || rawMessage.type) as 'user' | 'assistant' | 'system',
    content: rawMessage.content || '',
    timestamp: rawMessage.timestamp || Date.now(),
    
    // 思考过程数据
    thought: rawMessage.thought?.trim() || undefined,
    planThought: rawMessage.planThought?.trim() || undefined,
    toolThought: rawMessage.toolThought?.trim() || undefined,
    
    // 任务相关
    taskStatus: rawMessage.taskStatus as MESSAGE.MsgItem['taskStatus'],
    
    // 解析 JSON 字段
    resultMap: rawMessage.resultMap || safeParseJson(rawMessage.resultMapJson),
    
    // 工具调用结果
    toolResult: rawMessage.toolResult,
    
    // 执行计划
    plan: rawMessage.plan,
    
    // 任务列表
    tasks: rawMessage.tasks,
    
    // 文件附件
    files: rawMessage.files || [],
  };
};

/**
 * 标准化会话数据结构
 */
export const normalizeSessionData = (rawSession: any): HISTORY.SessionHistory => {
  return {
    id: `session_${rawSession.sessionId}`,
    sessionId: rawSession.sessionId,
    title: rawSession.title || '未命名会话',
    createTime: rawSession.createTime || Date.now(),
    updateTime: rawSession.updateTime || Date.now(),
    messageCount: typeof rawSession.messageCount === 'number' ? rawSession.messageCount : 0,
    status: (rawSession.status as HISTORY.SessionHistory['status']) || 'completed',
    tags: Array.isArray(rawSession.tags) 
      ? rawSession.tags 
      : (rawSession.tags ? String(rawSession.tags).split(',').filter(Boolean) : []),
    summary: rawSession.summary,
  };
};

/**
 * 标准化文件数据结构
 */
export const normalizeFileData = (rawFile: any): HISTORY.FileHistory => {
  return {
    id: rawFile.id || rawFile.fileId || `file-${Date.now()}-${Math.random()}`,
    sessionId: rawFile.sessionId,
    messageId: rawFile.messageId || '',
    fileName: rawFile.fileName || '未知文件',
    filePath: rawFile.filePath || rawFile.previewUrl || rawFile.downloadUrl || '',
    fileType: rawFile.fileType || 'unknown',
    fileSize: typeof rawFile.fileSize === 'number' ? rawFile.fileSize : (rawFile.size || 0),
    createTime: rawFile.createTime || Date.now(),
    downloadUrl: rawFile.downloadUrl,
  };
};

/**
 * 标准化事件数据结构
 */
export const normalizeEventData = (rawEvent: any): any => {
  const safeParsePayload = (payload: string | object) => {
    if (typeof payload === 'object') return payload;
    if (typeof payload === 'string') {
      try {
        return JSON.parse(payload);
      } catch {
        return null;
      }
    }
    return null;
  };

  return {
    id: rawEvent.id,
    sessionId: rawEvent.sessionId,
    messageId: rawEvent.messageId,
    messageType: rawEvent.messageType,
    messageOrder: rawEvent.messageOrder,
    taskId: rawEvent.taskId,
    taskOrder: rawEvent.taskOrder,
    isFinal: rawEvent.isFinal,
    payloadJson: rawEvent.payloadJson,
    payload: safeParsePayload(rawEvent.payloadJson),
    createTime: rawEvent.createTime || Date.now(),
  };
};

/**
 * 验证数据完整性
 */
export const validateMessageData = (message: HISTORY.MessageHistory): boolean => {
  return !!(
    message.id &&
    message.sessionId &&
    message.type &&
    message.content &&
    message.timestamp
  );
};

/**
 * 检查消息是否包含过程数据
 */
export const hasProcessData = (message: HISTORY.MessageHistory): boolean => {
  return !!(
    message.thought ||
    message.toolThought ||
    message.planThought ||
    message.toolResult ||
    message.resultMap?.searchResult ||
    message.plan ||
    message.tasks
  );
};

/**
 * 提取搜索结果数据
 */
export const extractSearchResults = (resultMap: any): any[] => {
  if (!resultMap?.searchResult) return [];
  
  const { docs } = resultMap.searchResult;
  if (!Array.isArray(docs)) return [];
  
  return docs.flat().filter(doc => doc && typeof doc === 'object');
};

/**
 * 格式化执行计划数据
 */
export const formatPlanData = (plan: any): any => {
  if (!plan || typeof plan !== 'object') return null;
  
  return {
    title: plan.title,
    steps: Array.isArray(plan.steps) ? plan.steps.map((step: any) => ({
      id: step.id,
      title: step.title || '未命名步骤',
      status: step.status || 'pending',
      description: step.description,
    })) : [],
    stages: plan.stages,
  };
};

/**
 * 批量转换消息数据
 */
export const normalizeMessagesArray = (rawMessages: any[]): HISTORY.MessageHistory[] => {
  if (!Array.isArray(rawMessages)) return [];
  
  return rawMessages
    .map(normalizeMessageData)
    .filter(validateMessageData)
    .sort((a, b) => a.timestamp - b.timestamp);
};

/**
 * 批量转换会话数据
 */
export const normalizeSessionsArray = (rawSessions: any[]): HISTORY.SessionHistory[] => {
  if (!Array.isArray(rawSessions)) return [];
  
  return rawSessions
    .map(normalizeSessionData)
    .sort((a, b) => b.updateTime - a.updateTime);
};

/**
 * 批量转换文件数据
 */
export const normalizeFilesArray = (rawFiles: any[]): HISTORY.FileHistory[] => {
  if (!Array.isArray(rawFiles)) return [];
  
  return rawFiles
    .map(normalizeFileData)
    .sort((a, b) => b.createTime - a.createTime);
};
