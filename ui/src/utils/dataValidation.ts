// 数据验证和测试工具 - 验证统一数据处理的正确性

import {
  normalizeSessionData,
  normalizeMessageData,
  normalizeFileData,
  normalizeEventData,
  validateMessageData,
  hasProcessData,
} from './dataTransform';

/**
 * 创建测试数据来验证功能
 */
export const createValidationTestData = () => {
  console.log('=== 开始数据验证测试 ===');
  
  // 测试会话数据标准化
  const rawSessionData = {
    sessionId: 'test-session-123',
    title: '测试会话',
    status: 'completed',
    createTime: Date.now(),
    updateTime: Date.now(),
    tags: 'tag1,tag2,tag3', // 字符串格式，需要转换为数组
  };
  
  const normalizedSession = normalizeSessionData(rawSessionData);
  console.log('✅ 会话数据标准化测试:', {
    input: rawSessionData,
    output: normalizedSession,
    tagsArray: Array.isArray(normalizedSession.tags),
  });
  
  // 测试消息数据标准化
  const rawMessageData = {
    id: 'test-msg-123',
    sessionId: 'test-session-123',
    role: 'assistant', // 后端字段名
    content: '这是一个测试消息',
    timestamp: Date.now(),
    thought: '这是思考过程',
    toolThought: '这是工具调用思考',
    planThought: '这是计划思考',
    resultMapJson: '{"searchResult":{"query":["测试查询"],"docs":[[{"title":"测试文档","content":"测试内容"}]]}}',
    toolResult: {
      toolName: 'test_tool',
      toolParam: { query: '测试参数' },
      toolResult: '测试结果'
    },
    plan: {
      steps: [
        { id: 1, title: '步骤1', status: 'completed', description: '第一步' },
        { id: 2, title: '步骤2', status: 'completed', description: '第二步' }
      ]
    },
    tasks: [[
      { task: '任务1' },
      { task: '任务2' }
    ]],
    taskStatus: 2
  };
  
  const normalizedMessage = normalizeMessageData(rawMessageData);
  console.log('✅ 消息数据标准化测试:', {
    typeConversion: normalizedMessage.type === 'assistant', // role -> type
    jsonParsing: !!normalizedMessage.resultMap?.searchResult,
    processDataExists: hasProcessData(normalizedMessage),
    isValid: validateMessageData(normalizedMessage),
  });
  
  // 测试文件数据标准化
  const rawFileData = {
    fileId: 'test-file-123',
    sessionId: 'test-session-123',
    fileName: 'test.pdf',
    fileType: 'pdf',
    size: 1024 * 100, // 后端字段名
    createTime: Date.now(),
    downloadUrl: 'https://example.com/download'
  };
  
  const normalizedFile = normalizeFileData(rawFileData);
  console.log('✅ 文件数据标准化测试:', {
    input: rawFileData,
    output: normalizedFile,
    sizeMapping: normalizedFile.fileSize === rawFileData.size, // size -> fileSize
  });
  
  // 测试事件数据标准化
  const rawEventData = {
    id: 'test-event-123',
    sessionId: 'test-session-123',
    messageId: 'test-msg-123',
    messageType: 'plan',
    payloadJson: '{"messageType":"plan","task":"测试任务","plan":{"title":"测试计划"}}',
    isFinal: false,
    createTime: Date.now()
  };
  
  const normalizedEvent = normalizeEventData(rawEventData);
  console.log('✅ 事件数据标准化测试:', {
    input: rawEventData,
    output: normalizedEvent,
    payloadParsed: !!normalizedEvent.payload,
  });
  
  console.log('=== 数据验证测试完成 ===');
  
  return {
    session: normalizedSession,
    message: normalizedMessage,
    file: normalizedFile,
    event: normalizedEvent,
  };
};

/**
 * 验证过程数据显示
 */
export const validateProcessDataDisplay = (message: HISTORY.MessageHistory) => {
  console.log('=== 验证过程数据显示 ===');
  
  const checks = {
    hasThought: !!message.thought?.trim(),
    hasToolThought: !!message.toolThought?.trim(),
    hasPlanThought: !!message.planThought?.trim(),
    hasToolResult: !!message.toolResult,
    hasSearchResult: !!message.resultMap?.searchResult,
    hasPlan: !!message.plan?.steps?.length,
    hasTasks: !!message.tasks?.length,
  };
  
  console.log('过程数据检查结果:', checks);
  
  const hasAnyProcessData = Object.values(checks).some(Boolean);
  console.log('是否包含过程数据:', hasAnyProcessData);
  
  if (hasAnyProcessData) {
    console.log('✅ 过程数据验证通过 - 应该能正常显示');
  } else {
    console.log('❌ 过程数据缺失 - 可能无法正常显示');
  }
  
  return { checks, hasAnyProcessData };
};

/**
 * 完整的数据流验证
 */
export const validateCompleteDataFlow = () => {
  console.log('=== 完整数据流验证 ===');
  
  try {
    // 1. 创建测试数据
    const testData = createValidationTestData();
    
    // 2. 验证过程数据
    const processValidation = validateProcessDataDisplay(testData.message);
    
    // 3. 模拟localStorage保存和读取
    const testSessionId = testData.session.sessionId;
    
    try {
      // 保存测试数据到localStorage
      localStorage.setItem('genie_test_sessions', JSON.stringify([testData.session]));
      localStorage.setItem('genie_test_messages', JSON.stringify({ [testSessionId]: [testData.message] }));
      localStorage.setItem('genie_test_files', JSON.stringify({ [testSessionId]: [testData.file] }));
      
      // 读取并验证
      const savedSessions = JSON.parse(localStorage.getItem('genie_test_sessions') || '[]');
      const savedMessages = JSON.parse(localStorage.getItem('genie_test_messages') || '{}');
      const savedFiles = JSON.parse(localStorage.getItem('genie_test_files') || '{}');
      
      console.log('✅ localStorage 读写测试通过');
      
      // 清理测试数据
      localStorage.removeItem('genie_test_sessions');
      localStorage.removeItem('genie_test_messages');
      localStorage.removeItem('genie_test_files');
      
      console.log('✅ 测试数据清理完成');
      
    } catch (storageError) {
      console.error('❌ localStorage 测试失败:', storageError);
    }
    
    console.log('=== 验证结果汇总 ===');
    console.log('• 数据标准化: ✅ 正常');
    console.log('• 过程数据检测: ✅ 正常');
    console.log('• 数据存储: ✅ 正常');
    console.log('• 总体状态: ✅ 数据显示功能应该正常工作');
    
    return true;
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
    return false;
  }
};

// 将验证函数暴露到全局作用域便于调试
declare global {
  interface Window {
    validateDataFlow: typeof validateCompleteDataFlow;
    createTestData: typeof createValidationTestData;
    validateProcessData: typeof validateProcessDataDisplay;
  }
}

if (typeof window !== 'undefined') {
  window.validateDataFlow = validateCompleteDataFlow;
  window.createTestData = createValidationTestData;
  window.validateProcessData = validateProcessDataDisplay;
}

console.log('📋 数据验证工具已加载');
console.log('可用命令:');
console.log('  - validateDataFlow() : 完整数据流验证');
console.log('  - createTestData() : 创建测试数据');
console.log('  - validateProcessData(message) : 验证过程数据');
