// 历史记录管理工具函数
// 新增功能：历史记录的localStorage持久化管理

import { getSessionId } from './utils';
import {
  normalizeSessionData,
  normalizeMessageData,
  normalizeFileData,
  normalizeMessagesArray,
  normalizeSessionsArray,
  normalizeFilesArray,
  validateMessageData,
} from './dataTransform';

// localStorage 键名常量
const STORAGE_KEYS = {
  SESSIONS: 'genie_history_sessions',
  MESSAGES: 'genie_history_messages',
  FILES: 'genie_history_files',
  SETTINGS: 'genie_history_settings',
} as const;

// 默认设置
const DEFAULT_SETTINGS = {
  maxSessions: 100,
  maxMessages: 1000,
  autoCleanDays: 30,
} as const;

/**
 * 获取localStorage中的数据
 */
function getStorageData<T>(key: string, defaultValue: T): T {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key: ${key}`, error);
    return defaultValue;
  }
}

/**
 * 设置localStorage中的数据
 */
function setStorageData<T>(key: string, data: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error writing to localStorage key: ${key}`, error);
  }
}

/**
 * 获取所有会话历史
 */
export function getSessionHistory(): HISTORY.SessionHistory[] {
  return getStorageData<HISTORY.SessionHistory[]>(STORAGE_KEYS.SESSIONS, []);
}

/**
 * 保存会话历史
 */
export function saveSessionHistory(session: HISTORY.SessionHistory): void {
  // 标准化会话数据
  const normalizedSession = normalizeSessionData(session);
  
  const sessions = getSessionHistory();
  const existingIndex = sessions.findIndex(s => s.sessionId === normalizedSession.sessionId);
  
  if (existingIndex >= 0) {
    sessions[existingIndex] = { ...sessions[existingIndex], ...normalizedSession, updateTime: Date.now() };
  } else {
    sessions.unshift({ ...normalizedSession, createTime: Date.now(), updateTime: Date.now() });
  }
  
  // 限制最大会话数量
  const settings = getStorageData(STORAGE_KEYS.SETTINGS, DEFAULT_SETTINGS);
  if (sessions.length > settings.maxSessions) {
    sessions.splice(settings.maxSessions);
  }
  
  setStorageData(STORAGE_KEYS.SESSIONS, sessions);
}

/**
 * 获取会话的消息历史
 */
export function getSessionMessages(sessionId: string): HISTORY.MessageHistory[] {
  const messagesData = getStorageData(STORAGE_KEYS.MESSAGES, {});
  
  let messages: HISTORY.MessageHistory[] = [];
  
  // 兼容两种数据格式：数组格式和对象格式
  if (Array.isArray(messagesData)) {
    // 数组格式：过滤出指定sessionId的消息
    messages = messagesData.filter(msg => msg.sessionId === sessionId);
  } else {
    // 对象格式：直接返回指定sessionId的消息数组
    messages = messagesData[sessionId] || [];
  }
  
  // 使用标准化处理确保数据一致性
  return normalizeMessagesArray(messages);
}

/**
 * 保存消息历史
 */
export function saveMessageHistory(message: HISTORY.MessageHistory): void {
  // 标准化消息数据
  const normalizedMessage = normalizeMessageData(message);
  
  // 验证数据有效性
  if (!validateMessageData(normalizedMessage)) {
    console.warn('Invalid message data, skipping save:', message);
    return;
  }
  
  const messages = getStorageData<HISTORY.MessageHistory[]>(STORAGE_KEYS.MESSAGES, []);
  messages.unshift(normalizedMessage);
  
  // 限制最大消息数量
  const settings = getStorageData(STORAGE_KEYS.SETTINGS, DEFAULT_SETTINGS);
  if (messages.length > settings.maxMessages) {
    messages.splice(settings.maxMessages);
  }
  
  setStorageData(STORAGE_KEYS.MESSAGES, messages);
}

/**
 * 获取会话的文件历史
 */
export function getSessionFiles(sessionId: string): HISTORY.FileHistory[] {
  const filesData = getStorageData(STORAGE_KEYS.FILES, {});
  
  let files: HISTORY.FileHistory[] = [];
  
  // 兼容两种数据格式：数组格式和对象格式
  if (Array.isArray(filesData)) {
    // 数组格式：过滤出指定sessionId的文件
    files = filesData.filter(file => file.sessionId === sessionId);
  } else {
    // 对象格式：直接返回指定sessionId的文件数组
    files = filesData[sessionId] || [];
  }
  
  // 使用标准化处理确保数据一致性
  return normalizeFilesArray(files);
}

/**
 * 保存文件历史
 */
export function saveFileHistory(file: HISTORY.FileHistory): void {
  // 标准化文件数据
  const normalizedFile = normalizeFileData(file);
  
  const files = getStorageData<HISTORY.FileHistory[]>(STORAGE_KEYS.FILES, []);
  files.unshift(normalizedFile);
  setStorageData(STORAGE_KEYS.FILES, files);
}

/**
 * 删除会话及其相关数据
 */
export function deleteSession(sessionId: string): void {
  // 删除会话
  const sessions = getSessionHistory().filter(s => s.sessionId !== sessionId);
  setStorageData(STORAGE_KEYS.SESSIONS, sessions);
  
  // 删除消息
  const messages = getStorageData<HISTORY.MessageHistory[]>(STORAGE_KEYS.MESSAGES, [])
    .filter(msg => msg.sessionId !== sessionId);
  setStorageData(STORAGE_KEYS.MESSAGES, messages);
  
  // 删除文件
  const files = getStorageData<HISTORY.FileHistory[]>(STORAGE_KEYS.FILES, [])
    .filter(file => file.sessionId !== sessionId);
  setStorageData(STORAGE_KEYS.FILES, files);
}

/**
 * 搜索会话历史
 */
export function searchSessions(filter: HISTORY.SearchFilter): HISTORY.SessionHistory[] {
  let sessions = getSessionHistory();
  
  // 关键词搜索
  if (filter.keyword) {
    const keyword = filter.keyword.toLowerCase();
    sessions = sessions.filter(session => 
      session.title.toLowerCase().includes(keyword) ||
      session.summary?.toLowerCase().includes(keyword)
    );
  }
  
  // 日期范围筛选
  if (filter.dateRange) {
    const [startTime, endTime] = filter.dateRange;
    sessions = sessions.filter(session => 
      session.createTime >= startTime && session.createTime <= endTime
    );
  }
  
  // 状态筛选
  if (filter.status && filter.status.length > 0) {
    sessions = sessions.filter(session => filter.status!.includes(session.status));
  }
  
  // 标签筛选
  if (filter.tags && filter.tags.length > 0) {
    sessions = sessions.filter(session => 
      filter.tags!.some(tag => session.tags.includes(tag))
    );
  }
  
  // 产品类型筛选
  if (filter.productType) {
    sessions = sessions.filter(session => session.product?.type === filter.productType);
  }
  
  // 排序
  const sortBy = filter.sortBy || 'updateTime';
  const sortOrder = filter.sortOrder || 'desc';
  sessions.sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
  });
  
  return sessions;
}

/**
 * 获取历史记录统计信息
 */
export function getHistoryStats(): HISTORY.HistoryStats {
  const sessions = getSessionHistory();
  const messages = getStorageData<HISTORY.MessageHistory[]>(STORAGE_KEYS.MESSAGES, []);
  const files = getStorageData<HISTORY.FileHistory[]>(STORAGE_KEYS.FILES, []);
  
  // 计算最近7天的活动
  const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
  const recentActivity = sessions.filter(s => s.updateTime > sevenDaysAgo).length;
  
  return {
    totalSessions: sessions.length,
    totalMessages: messages.length,
    totalFiles: files.length,
    recentActivity,
  };
}

/**
 * 清理过期数据
 */
export function cleanExpiredData(): void {
  const settings = getStorageData(STORAGE_KEYS.SETTINGS, DEFAULT_SETTINGS);
  const expireTime = Date.now() - settings.autoCleanDays * 24 * 60 * 60 * 1000;
  
  // 清理过期会话
  const sessions = getSessionHistory().filter(s => s.updateTime > expireTime);
  setStorageData(STORAGE_KEYS.SESSIONS, sessions);
  
  // 获取有效的sessionId列表
  const validSessionIds = new Set(sessions.map(s => s.sessionId));
  
  // 清理孤立的消息
  const messages = getStorageData<HISTORY.MessageHistory[]>(STORAGE_KEYS.MESSAGES, [])
    .filter(msg => validSessionIds.has(msg.sessionId));
  setStorageData(STORAGE_KEYS.MESSAGES, messages);
  
  // 清理孤立的文件
  const files = getStorageData<HISTORY.FileHistory[]>(STORAGE_KEYS.FILES, [])
    .filter(file => validSessionIds.has(file.sessionId));
  setStorageData(STORAGE_KEYS.FILES, files);
}

/**
 * 导出历史数据
 */
export function exportHistoryData(): HISTORY.ExportData {
  return {
    sessions: getSessionHistory(),
    messages: getStorageData<HISTORY.MessageHistory[]>(STORAGE_KEYS.MESSAGES, []),
    files: getStorageData<HISTORY.FileHistory[]>(STORAGE_KEYS.FILES, []),
    exportTime: Date.now(),
    version: '1.0.0',
  };
}

/**
 * 从ChatItem转换为历史记录格式
 */
export function convertChatItemToHistory(chatItem: CHAT.ChatItem): {
  session: HISTORY.SessionHistory;
  messages: HISTORY.MessageHistory[];
  files: HISTORY.FileHistory[];
} {
  const sessionId = chatItem.sessionId || getSessionId();
  const now = Date.now();
  
  // 创建会话记录
  const session: HISTORY.SessionHistory = {
    id: `session_${sessionId}`,
    sessionId,
    title: chatItem.query.slice(0, 50) + (chatItem.query.length > 50 ? '...' : ''),
    createTime: now,
    updateTime: now,
    messageCount: 1 + (chatItem.response ? 1 : 0),
    status: chatItem.loading ? 'running' : (chatItem.taskStatus === 3 ? 'error' : 'completed'),
    tags: [],
    summary: chatItem.response?.slice(0, 100),
  };
  
  // 创建消息记录
  const messages: HISTORY.MessageHistory[] = [];
  
  // 用户消息
  messages.push({
    id: `msg_${sessionId}_user_${now}`,
    sessionId,
    type: 'user',
    content: chatItem.query,
    timestamp: now,
    files: chatItem.files,
  });
  
  // 助手回复
  if (chatItem.response) {
    const assistantMessage: HISTORY.MessageHistory = {
      id: `msg_${sessionId}_assistant_${now + 1}`,
      sessionId,
      type: 'assistant',
      content: chatItem.response,
      timestamp: now + 1,
      tasks: chatItem.tasks,
      plan: chatItem.plan,
      thought: chatItem.thought,
      taskStatus: chatItem.taskStatus,
    };
    
    // 从multiAgent.tasks中提取详细的任务过程数据
    if (chatItem.multiAgent?.tasks && chatItem.multiAgent.tasks.length > 0) {
      // multiAgent.tasks是二维数组，需要先展平
      const allTasks = chatItem.multiAgent.tasks.flat();
      
      // 提取思考过程
      const toolThoughts = allTasks
        .map(task => task.toolThought)
        .filter(Boolean);
      if (toolThoughts.length > 0) {
        assistantMessage.toolThought = toolThoughts.join('\n\n');
      }
      
      // 提取计划思考
      const planThoughts = allTasks
        .map(task => task.planThought)
        .filter(Boolean);
      if (planThoughts.length > 0) {
        assistantMessage.planThought = planThoughts.join('\n\n');
      }
      
      // 提取工具调用结果
      const toolResults = allTasks
        .filter(task => task.toolResult)
        .map(task => task.toolResult);
      if (toolResults.length > 0) {
        assistantMessage.toolResult = toolResults[0]; // 取第一个工具结果
      }
      
      // 提取搜索结果
      const searchResults = allTasks
        .filter(task => task.resultMap?.searchResult)
        .map(task => task.resultMap!.searchResult);
      if (searchResults.length > 0) {
        assistantMessage.resultMap = {
          ...assistantMessage.resultMap,
          searchResult: searchResults[0], // 取第一个搜索结果
        };
      }
      
      // 提取其他resultMap数据
      const resultMaps = allTasks
        .filter(task => task.resultMap)
        .map(task => task.resultMap!);
      if (resultMaps.length > 0) {
        // 合并所有resultMap数据
        const mergedResultMap = resultMaps.reduce((acc, curr) => {
          return {
            ...acc,
            ...curr,
            // 特殊处理搜索结果，避免被覆盖
            searchResult: acc.searchResult || curr.searchResult,
          };
        }, assistantMessage.resultMap || {});
        assistantMessage.resultMap = mergedResultMap;
      }
    }
    
    // 如果有计划思考，也要保存
    if (chatItem.multiAgent?.plan_thought) {
      assistantMessage.planThought = chatItem.multiAgent.plan_thought;
    }
    
    // 保存基础思考内容
    if (chatItem.thought) {
      assistantMessage.thought = chatItem.thought;
    }
    
    messages.push(assistantMessage);
  }
  
  // 创建文件记录
  const files: HISTORY.FileHistory[] = [];
  if (chatItem.files) {
    chatItem.files.forEach((file, index) => {
      files.push({
        id: `file_${sessionId}_${now}_${index}`,
        sessionId,
        messageId: `msg_${sessionId}_user_${now}`,
        fileName: file.name,
        filePath: file.url,
        fileType: file.type,
        fileSize: file.size,
        createTime: now,
        downloadUrl: file.url,
      });
    });
  }
  
  return { session, messages, files };
}