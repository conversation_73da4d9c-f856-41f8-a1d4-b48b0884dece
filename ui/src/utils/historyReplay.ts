/**
 * 历史回放数据处理工具
 * 用于将历史事件数据重构为与实时执行一致的聊天格式
 */

import { combineData, handleTaskData } from './chat';
import { standardizeEventData, sortEventsBySequence, validateEventData, repairEventData } from './dataConsistency';

/**
 * 解析事件载荷数据
 */
function parseEventPayload(evt: any): MESSAGE.EventData | null {
  if (!evt?.payloadJson) return null;

  try {
    const obj = JSON.parse(evt.payloadJson);

    // 改进的数据结构解析逻辑
    let eventData: MESSAGE.EventData | null = null;

    if (obj?.eventData) {
      // 新的数据结构：{ eventData: {...} }
      const raw = obj.eventData;
      eventData = {
        messageType: raw.messageType || evt.messageType,
        messageId: raw.messageId || evt.messageId,
        messageOrder: evt.messageOrder || raw.messageOrder || 0,
        taskId: raw.taskId || evt.taskId,
        taskOrder: evt.taskOrder || raw.taskOrder || 0,
        resultMap: raw.resultMap || {},
        plan: raw.plan,
        planThought: raw.planThought,
        toolThought: raw.toolThought,
        task: raw.task,
        result: raw.result,
        toolResult: raw.toolResult,
        taskSummary: raw.taskSummary,
        // 确保时间戳一致性
        createTime: evt.createTime,
        isFinal: raw.isFinal || evt.isFinal || false,
      };
    } else {
      // 兼容旧的数据结构：直接 {...}
      const raw = obj;
      const innerType = raw?.resultMap?.messageType || raw?.messageType || evt.messageType;

      const TASK_INNER_TYPES = new Set([
        'tool_thought', 'deep_search', 'result', 'code', 'html', 'markdown', 'ppt',
        'tool_result', 'file', 'browser', 'task_summary', 'knowledge'
      ]);

      if (TASK_INNER_TYPES.has(innerType)) {
        eventData = {
          messageType: 'task',
          taskId: raw?.taskId || evt?.taskId,
          messageId: raw?.messageId || evt?.messageId,
          messageOrder: evt.messageOrder || raw.messageOrder || 0,
          taskOrder: evt.taskOrder || raw.taskOrder || 0,
          resultMap: { ...(raw?.resultMap || {}), messageType: innerType },
          createTime: evt.createTime,
          isFinal: raw.isFinal || evt.isFinal || false,
        };
      } else {
        eventData = {
          messageType: raw?.messageType || evt.messageType,
          taskId: raw?.taskId || evt?.taskId,
          messageId: raw?.messageId || evt?.messageId,
          messageOrder: evt.messageOrder || raw.messageOrder || 0,
          taskOrder: evt.taskOrder || raw.taskOrder || 0,
          resultMap: raw?.resultMap || {},
          plan: raw?.plan,
          planThought: raw?.planThought,
          toolThought: raw?.toolThought,
          task: raw?.task,
          result: raw?.result,
          toolResult: raw?.toolResult,
          taskSummary: raw?.taskSummary,
          createTime: evt.createTime,
          isFinal: raw.isFinal || evt.isFinal || false,
        };
      }
    }

    // 确保事件数据完整性
    if (eventData?.messageType) {
      // 补充缺失的字段
      if (!eventData.messageId && evt.messageId) {
        eventData.messageId = evt.messageId;
      }
      if (!eventData.taskId && evt.taskId) {
        eventData.taskId = evt.taskId;
      }

      return eventData;
    }

    return null;
  } catch (error) {
    console.warn('Failed to parse event payload:', error, evt);
    return null;
  }
}

/**
 * 基于历史事件重建执行时视图的 Chat 结构
 */
export function buildReplayChat(
  events: any[],
  messages: any[],
  sessionId: string,
  session?: any
): CHAT.ChatItem | null {
  if (!Array.isArray(events) || events.length === 0) return null;

  const userMsg = (messages || []).find((m: any) => m.type === 'user');
  let chat: CHAT.ChatItem = {
    query: userMsg?.content || session?.title || '',
    files: userMsg?.files || [],
    responseType: 'txt',
    sessionId,
    requestId: `replay-${sessionId}`,
    loading: false,
    forceStop: false,
    tasks: [],
    thought: '',
    response: '',
    taskStatus: 0,
    tip: '',
    multiAgent: { tasks: [] },
  } as CHAT.ChatItem;

  // 使用新的数据一致性工具处理事件
  const standardizedEvents = events.map(evt => {
    const standardized = standardizeEventData(evt, false);
    const validation = validateEventData(standardized);

    if (!validation.isValid) {
      console.warn('Event data validation failed:', validation.missingFields, evt);
      return repairEventData(standardized);
    }

    if (validation.warnings.length > 0) {
      console.warn('Event data warnings:', validation.warnings, evt);
    }

    return standardized;
  });

  // 按序列排序事件
  const sortedEvents = sortEventsBySequence(standardizedEvents);

  // 回放事件，复用在线执行时的数据合并逻辑
  for (const standardizedEvent of sortedEvents) {
    const eventData = convertStandardizedToEventData(standardizedEvent);
    if (eventData) {
      chat = combineData(eventData, chat);
    }
  }

  // 生成任务与计划数据，提供给 Dialogue 渲染
  try {
    const taskData = handleTaskData(chat, true, chat.multiAgent);
    chat = taskData.currentChat;
  } catch (error) {
    console.warn('Failed to handle task data:', error);
  }

  return chat;
}

/**
 * 将标准化事件数据转换为MESSAGE.EventData格式
 */
function convertStandardizedToEventData(standardized: any): MESSAGE.EventData | null {
  if (!standardized.messageType) return null;

  return {
    messageType: standardized.messageType,
    messageId: standardized.messageId,
    messageOrder: standardized.messageOrder,
    taskId: standardized.taskId,
    taskOrder: standardized.taskOrder,
    resultMap: standardized.resultMap || {},
    plan: standardized.plan,
    planThought: standardized.planThought,
    toolThought: standardized.toolThought,
    task: standardized.task,
    result: standardized.result,
    toolResult: standardized.toolResult,
    taskSummary: standardized.taskSummary,
    isFinal: standardized.isFinal,
    createTime: standardized.createTime,
  } as MESSAGE.EventData;
}

/**
 * 验证历史数据完整性
 */
export function validateHistoryData(events: any[], messages: any[], files: any[]): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // 检查事件数据
  if (!Array.isArray(events)) {
    issues.push('Events data is not an array');
  } else {
    events.forEach((evt, index) => {
      if (!evt.payloadJson) {
        issues.push(`Event ${index} missing payloadJson`);
      }
      if (!evt.messageType) {
        issues.push(`Event ${index} missing messageType`);
      }
      if (!evt.createTime) {
        issues.push(`Event ${index} missing createTime`);
      }
    });
  }

  // 检查消息数据
  if (!Array.isArray(messages)) {
    issues.push('Messages data is not an array');
  } else {
    const hasUserMessage = messages.some(m => m.type === 'user');
    if (!hasUserMessage) {
      issues.push('No user message found');
    }
  }

  // 检查文件数据
  if (!Array.isArray(files)) {
    issues.push('Files data is not an array');
  }

  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * 获取历史数据统计信息
 */
export function getHistoryStats(events: any[], messages: any[], files: any[]): {
  eventCount: number;
  messageCount: number;
  fileCount: number;
  eventTypes: Record<string, number>;
  timeRange: { start: number; end: number } | null;
} {
  const eventTypes: Record<string, number> = {};
  let timeRange: { start: number; end: number } | null = null;

  if (Array.isArray(events) && events.length > 0) {
    events.forEach(evt => {
      if (evt.messageType) {
        eventTypes[evt.messageType] = (eventTypes[evt.messageType] || 0) + 1;
      }
    });

    const times = events
      .map(evt => evt.createTime)
      .filter(time => typeof time === 'number')
      .sort((a, b) => a - b);

    if (times.length > 0) {
      timeRange = {
        start: times[0],
        end: times[times.length - 1]
      };
    }
  }

  return {
    eventCount: Array.isArray(events) ? events.length : 0,
    messageCount: Array.isArray(messages) ? messages.length : 0,
    fileCount: Array.isArray(files) ? files.length : 0,
    eventTypes,
    timeRange
  };
}
