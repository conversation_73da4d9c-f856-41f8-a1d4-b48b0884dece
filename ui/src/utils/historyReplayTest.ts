/**
 * 历史回放测试工具
 * 用于测试和验证历史回放功能的完整性
 */

import { buildReplayChat } from './historyReplay';

/**
 * 测试历史回放数据完整性
 */
export function testHistoryReplayCompleteness(
  events: any[], 
  messages: any[], 
  sessionId: string,
  session?: any
): {
  success: boolean;
  replayChat: any;
  analysis: {
    totalEvents: number;
    processedEvents: number;
    skippedEvents: number;
    eventTypes: Record<string, number>;
    hasUserQuery: boolean;
    hasResponse: boolean;
    hasTasks: boolean;
    taskCount: number;
    missingData: string[];
  };
  recommendations: string[];
} {
  const analysis = {
    totalEvents: events.length,
    processedEvents: 0,
    skippedEvents: 0,
    eventTypes: {} as Record<string, number>,
    hasUserQuery: false,
    hasResponse: false,
    hasTasks: false,
    taskCount: 0,
    missingData: [] as string[],
  };

  const recommendations: string[] = [];

  try {
    // 分析事件类型分布
    events.forEach(event => {
      const messageType = event.messageType || 'unknown';
      analysis.eventTypes[messageType] = (analysis.eventTypes[messageType] || 0) + 1;
    });

    // 构建回放聊天数据
    const replayChat = buildReplayChat(events, messages, sessionId, session);

    if (!replayChat) {
      return {
        success: false,
        replayChat: null,
        analysis,
        recommendations: ['无法构建回放聊天数据，请检查输入数据格式'],
      };
    }

    // 分析回放数据完整性
    analysis.hasUserQuery = !!replayChat.query;
    analysis.hasResponse = !!replayChat.response;
    analysis.hasTasks = !!(replayChat.tasks && replayChat.tasks.length > 0);
    analysis.taskCount = replayChat.tasks?.length || 0;

    // 检查缺失的数据
    if (!analysis.hasUserQuery) {
      analysis.missingData.push('用户查询内容');
    }
    if (!analysis.hasResponse) {
      analysis.missingData.push('AI响应内容');
    }
    if (!analysis.hasTasks) {
      analysis.missingData.push('任务执行数据');
    }

    // 生成建议
    if (analysis.missingData.length === 0) {
      recommendations.push('✅ 历史回放数据完整，所有关键信息都已正确重建');
    } else {
      recommendations.push(`⚠️ 发现 ${analysis.missingData.length} 项缺失数据: ${analysis.missingData.join(', ')}`);
    }

    if (analysis.eventTypes.heartbeat && analysis.eventTypes.heartbeat > 10) {
      recommendations.push('💡 建议启用心跳数据过滤功能以提升显示效果');
    }

    if (analysis.eventTypes.tool_thought && !replayChat.thought) {
      recommendations.push('🔧 工具思考数据存在但未正确提取，请检查数据解析逻辑');
    }

    if (analysis.eventTypes.deep_search && !replayChat.response) {
      recommendations.push('🔍 深度搜索数据存在但响应内容缺失，请检查搜索结果提取逻辑');
    }

    const successRate = (analysis.totalEvents - analysis.skippedEvents) / analysis.totalEvents;
    const isSuccess = successRate > 0.8 && analysis.missingData.length <= 1;

    return {
      success: isSuccess,
      replayChat,
      analysis,
      recommendations,
    };

  } catch (error) {
    return {
      success: false,
      replayChat: null,
      analysis,
      recommendations: [`测试过程中发生错误: ${error}`],
    };
  }
}

/**
 * 生成历史回放测试报告
 */
export function generateReplayTestReport(testResult: any): string {
  const { success, analysis, recommendations } = testResult;

  const eventTypesReport = Object.entries(analysis.eventTypes)
    .map(([type, count]) => `  • ${type}: ${count} 个`)
    .join('\n');

  return `
历史回放功能测试报告
====================

测试结果: ${success ? '✅ 通过' : '❌ 失败'}

数据统计:
• 总事件数: ${analysis.totalEvents}
• 已处理事件: ${analysis.processedEvents}
• 跳过事件: ${analysis.skippedEvents}
• 任务数量: ${analysis.taskCount}

事件类型分布:
${eventTypesReport}

数据完整性检查:
• 用户查询: ${analysis.hasUserQuery ? '✅' : '❌'}
• AI响应: ${analysis.hasResponse ? '✅' : '❌'}
• 任务数据: ${analysis.hasTasks ? '✅' : '❌'}

${analysis.missingData.length > 0 ? `缺失数据:\n${analysis.missingData.map(item => `• ${item}`).join('\n')}\n` : ''}

建议和优化:
${recommendations.map(r => `• ${r}`).join('\n')}
  `;
}

/**
 * 在控制台运行历史回放测试
 */
export async function runReplayTest(sessionId: string): Promise<void> {
  console.log('开始历史回放功能测试...');
  console.log('会话ID:', sessionId);

  try {
    // 获取历史数据
    const response = await fetch(`/api/history/sessions/${sessionId}/events`);
    const data = await response.json();

    if (!data.success || !data.data) {
      console.error('❌ 无法获取历史数据');
      return;
    }

    const { events, messages, session } = data.data;
    const result = testHistoryReplayCompleteness(events, messages, sessionId, session);
    const report = generateReplayTestReport(result);

    console.log(report);

    // 如果有问题，提供调试信息
    if (!result.success) {
      console.group('🔍 调试信息');
      console.log('回放聊天数据:', result.replayChat);
      console.log('原始事件数据:', events.slice(0, 3)); // 只显示前3个事件
      console.log('原始消息数据:', messages);
      console.groupEnd();
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

/**
 * 比较实时数据和历史回放数据的差异
 */
export function compareRealtimeAndReplay(
  realtimeChat: any,
  replayChat: any
): {
  identical: boolean;
  differences: Array<{
    field: string;
    realtimeValue: any;
    replayValue: any;
  }>;
  summary: string;
} {
  const differences: Array<{
    field: string;
    realtimeValue: any;
    replayValue: any;
  }> = [];

  // 比较关键字段
  const fieldsToCompare = ['query', 'response', 'taskStatus', 'thought'];

  fieldsToCompare.forEach(field => {
    const realtimeValue = realtimeChat?.[field];
    const replayValue = replayChat?.[field];

    if (realtimeValue !== replayValue) {
      differences.push({
        field,
        realtimeValue,
        replayValue,
      });
    }
  });

  // 比较任务数量
  const realtimeTaskCount = realtimeChat?.tasks?.length || 0;
  const replayTaskCount = replayChat?.tasks?.length || 0;

  if (realtimeTaskCount !== replayTaskCount) {
    differences.push({
      field: 'taskCount',
      realtimeValue: realtimeTaskCount,
      replayValue: replayTaskCount,
    });
  }

  const identical = differences.length === 0;
  const summary = identical 
    ? '✅ 实时数据和历史回放数据完全一致'
    : `⚠️ 发现 ${differences.length} 项差异`;

  return {
    identical,
    differences,
    summary,
  };
}

/**
 * 批量测试多个会话的历史回放功能
 */
export async function batchReplayTest(sessionIds: string[]): Promise<{
  totalSessions: number;
  passedSessions: number;
  failedSessions: number;
  results: Array<{
    sessionId: string;
    success: boolean;
    completeness: number;
    issues: string[];
  }>;
}> {
  const results = [];
  let passedSessions = 0;
  let failedSessions = 0;

  for (const sessionId of sessionIds) {
    try {
      const response = await fetch(`/api/history/sessions/${sessionId}/events`);
      const data = await response.json();

      if (!data.success || !data.data) {
        results.push({
          sessionId,
          success: false,
          completeness: 0,
          issues: ['无法获取历史数据'],
        });
        failedSessions++;
        continue;
      }

      const { events, messages, session } = data.data;
      const testResult = testHistoryReplayCompleteness(events, messages, sessionId, session);

      const completeness = testResult.analysis.missingData.length === 0 ? 100 : 
        Math.max(0, 100 - (testResult.analysis.missingData.length * 25));

      const issues = testResult.recommendations.filter(r => r.includes('⚠️') || r.includes('❌'));

      results.push({
        sessionId,
        success: testResult.success,
        completeness,
        issues: issues.length > 0 ? issues : [],
      });

      if (testResult.success) {
        passedSessions++;
      } else {
        failedSessions++;
      }

    } catch (error) {
      results.push({
        sessionId,
        success: false,
        completeness: 0,
        issues: [`测试错误: ${error}`],
      });
      failedSessions++;
    }
  }

  return {
    totalSessions: sessionIds.length,
    passedSessions,
    failedSessions,
    results,
  };
}
