/**
 * 页面状态快照捕获工具
 * 用于捕获ActionView三个标签页的完整状态
 */

import { PAGE_SNAPSHOT } from '@/types/pageSnapshot';
import { getUniqId } from '@/utils';

/**
 * 从任务列表中提取浏览器页面状态
 */
export function extractBrowserPageState(taskList: any[]): PAGE_SNAPSHOT.BrowserPageState {
  const searchHistory: PAGE_SNAPSHOT.BrowserPageState['searchHistory'] = [];
  
  taskList.forEach(task => {
    const { toolResult, resultMap, id, messageTime } = task;
    const { toolParam } = toolResult || {};
    const { searchResult } = resultMap || {};
    
    // 提取搜索结果
    const results: any[] = [];
    
    // 从不同位置提取搜索结果
    if (searchResult?.searchResult) {
      results.push(...searchResult.searchResult);
    }
    
    if (resultMap?.searchResult) {
      results.push(...resultMap.searchResult);
    }
    
    // 从toolResult中提取
    if (toolResult?.toolResult) {
      try {
        const toolData = typeof toolResult.toolResult === 'string' 
          ? JSON.parse(toolResult.toolResult) 
          : toolResult.toolResult;
        
        if (toolData.searchResult) {
          results.push(...toolData.searchResult);
        }
      } catch (error) {
        console.warn('Failed to parse tool result:', error);
      }
    }
    
    if (results.length > 0) {
      searchHistory.push({
        id: id || getUniqId(),
        query: toolParam?.query || searchResult?.query || '未知查询',
        timestamp: messageTime ? parseInt(messageTime) : Date.now(),
        resultCount: results.length,
        results: results.map(result => ({
          name: result.name || result.title || '未知标题',
          url: result.url || result.link || '',
          pageContent: result.pageContent || result.content || result.snippet
        }))
      });
    }
  });
  
  return {
    searchHistory: searchHistory.sort((a, b) => a.timestamp - b.timestamp),
    activeSearchId: searchHistory.length > 0 ? searchHistory[searchHistory.length - 1].id : undefined
  };
}

/**
 * 从任务列表中提取文件页面状态
 */
export function extractFilePageState(taskList: any[]): PAGE_SNAPSHOT.FilePageState {
  const fileList: PAGE_SNAPSHOT.FilePageState['fileList'] = [];
  
  taskList.forEach(task => {
    const { resultMap, messageTime, taskId } = task;
    const { fileInfo } = resultMap || {};
    
    if (fileInfo && Array.isArray(fileInfo)) {
      fileInfo.forEach((file: any) => {
        fileList.push({
          id: file.id || getUniqId(),
          name: file.fileName || file.name || '未知文件',
          type: file.fileType || file.type || getFileTypeFromName(file.fileName),
          url: file.domainUrl || file.url || file.previewUrl || '',
          downloadUrl: file.ossUrl || file.downloadUrl,
          size: file.fileSize || file.size,
          timestamp: messageTime ? parseInt(messageTime) : Date.now(),
          taskId: taskId,
          content: file.content // 如果有内容预览
        });
      });
    }
  });
  
  return {
    fileList: fileList.sort((a, b) => a.timestamp - b.timestamp),
    activeFileId: fileList.length > 0 ? fileList[fileList.length - 1].id : undefined
  };
}

/**
 * 从任务列表中提取实时跟踪页面状态
 */
export function extractFollowPageState(
  taskList: any[], 
  activeTask?: any,
  currentTaskIndex?: number
): PAGE_SNAPSHOT.FollowPageState {
  // 构建任务时序
  const taskTimeline: PAGE_SNAPSHOT.FollowPageState['taskTimeline'] = [];
  
  taskList.forEach(task => {
    taskTimeline.push({
      taskId: task.taskId || task.id,
      messageType: task.messageType,
      timestamp: task.messageTime ? parseInt(task.messageTime) : Date.now(),
      content: {
        resultMap: task.resultMap,
        toolResult: task.toolResult,
        isFinal: task.isFinal
      }
    });
  });
  
  return {
    activeTask: activeTask,
    taskList: taskList,
    currentTaskIndex: currentTaskIndex || (taskList.length > 0 ? taskList.length - 1 : 0),
    sliderValue: currentTaskIndex || (taskList.length > 0 ? taskList.length - 1 : 0),
    taskTimeline: taskTimeline.sort((a, b) => a.timestamp - b.timestamp)
  };
}

/**
 * 捕获完整的页面状态快照
 */
export function captureCompletePageSnapshot(
  sessionId: string,
  chatItem: CHAT.ChatItem,
  actionViewState: {
    activeView: 'follow' | 'browser' | 'file';
    taskList: any[];
    activeTask?: any;
    currentTaskIndex?: number;
  },
  options: {
    type?: 'auto' | 'manual';
    saveReason?: 'task_completed' | 'user_manual' | 'auto_interval';
  } = {}
): PAGE_SNAPSHOT.CompletePageSnapshot {
  
  const { activeView, taskList, activeTask, currentTaskIndex } = actionViewState;
  const { type = 'auto', saveReason = 'task_completed' } = options;
  
  const snapshot: PAGE_SNAPSHOT.CompletePageSnapshot = {
    id: `snapshot_${getUniqId()}`,
    sessionId,
    timestamp: Date.now(),
    type,
    taskStatus: chatItem.taskStatus || 0,
    isFinal: chatItem.loading === false && chatItem.taskStatus === 3,
    
    activeView,
    
    followPage: extractFollowPageState(taskList, activeTask, currentTaskIndex),
    browserPage: extractBrowserPageState(taskList),
    filePage: extractFilePageState(taskList),
    
    chatInfo: {
      query: chatItem.query,
      response: chatItem.response,
      thought: chatItem.thought,
      plan: chatItem.plan
    },
    
    metadata: {
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      saveReason
    }
  };
  
  return snapshot;
}

/**
 * 验证快照数据完整性
 */
export function validateSnapshot(snapshot: PAGE_SNAPSHOT.CompletePageSnapshot): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 必需字段检查
  if (!snapshot.id) errors.push('快照ID缺失');
  if (!snapshot.sessionId) errors.push('会话ID缺失');
  if (!snapshot.timestamp) errors.push('时间戳缺失');
  
  // 数据完整性检查
  if (!snapshot.chatInfo.query) warnings.push('用户查询内容为空');
  if (!snapshot.followPage.taskList.length) warnings.push('任务列表为空');
  
  // 文件数据检查
  if (snapshot.filePage.fileList.length === 0) {
    warnings.push('没有生成文件');
  } else {
    snapshot.filePage.fileList.forEach((file, index) => {
      if (!file.url && !file.downloadUrl) {
        warnings.push(`文件${index + 1}缺少访问链接`);
      }
    });
  }
  
  // 搜索数据检查
  if (snapshot.browserPage.searchHistory.length === 0) {
    warnings.push('没有搜索记录');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 从文件名推断文件类型
 */
function getFileTypeFromName(fileName?: string): string {
  if (!fileName) return 'unknown';
  
  const ext = fileName.split('.').pop()?.toLowerCase();
  
  const typeMap: Record<string, string> = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'txt': 'text/plain',
    'md': 'text/markdown',
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    'json': 'application/json',
    'xml': 'application/xml',
    'csv': 'text/csv',
    'zip': 'application/zip',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml'
  };
  
  return typeMap[ext || ''] || 'application/octet-stream';
}

/**
 * 生成快照预览信息
 */
export function generateSnapshotPreview(snapshot: PAGE_SNAPSHOT.CompletePageSnapshot): PAGE_SNAPSHOT.SnapshotListItem['preview'] {
  return {
    query: snapshot.chatInfo.query.slice(0, 100) + (snapshot.chatInfo.query.length > 100 ? '...' : ''),
    fileCount: snapshot.filePage.fileList.length,
    searchCount: snapshot.browserPage.searchHistory.length,
    taskCount: snapshot.followPage.taskList.length
  };
}
