#!/bin/bash

# JoyAgent-JDGenie 项目安全更新脚本
# 此脚本会保留本地配置文件的修改，同时拉取远程更新

set -e

echo "🔍 检查项目更新状态..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/Users/<USER>/git_project_vscode/04-mutil-agents/joyagent-jdgenie"
cd "$PROJECT_ROOT"

# 重要配置文件列表
CONFIG_FILES=(
    "genie-backend/src/main/resources/application.yml"
    "genie-tool/.env"
    "genie-tool/.env_template"
)

echo -e "${BLUE}📋 需要保护的配置文件:${NC}"
for file in "${CONFIG_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "  ✅ $file"
    else
        echo -e "  ❌ $file (不存在)"
    fi
done

# 1. 备份配置文件
echo -e "\n${YELLOW}📦 备份配置文件...${NC}"
BACKUP_DIR="config_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

for file in "${CONFIG_FILES[@]}"; do
    if [ -f "$file" ]; then
        # 创建备份目录结构
        backup_path="$BACKUP_DIR/$file"
        mkdir -p "$(dirname "$backup_path")"
        cp "$file" "$backup_path"
        echo -e "  ✅ 已备份: $file -> $backup_path"
    fi
done

# 2. 检查远程更新
echo -e "\n${BLUE}🔄 检查远程更新...${NC}"
git fetch origin

# 获取远程更新信息
COMMITS_BEHIND=$(git rev-list --count HEAD..origin/main)
if [ "$COMMITS_BEHIND" -eq 0 ]; then
    echo -e "${GREEN}✅ 项目已是最新版本，无需更新${NC}"
    exit 0
fi

echo -e "${YELLOW}📊 发现 $COMMITS_BEHIND 个新提交${NC}"
echo -e "\n${BLUE}📝 新提交列表:${NC}"
git log --oneline HEAD..origin/main

echo -e "\n${BLUE}📁 远程更新涉及的文件:${NC}"
git diff HEAD origin/main --name-only

# 3. 检查配置文件冲突
echo -e "\n${YELLOW}🔍 检查配置文件是否有冲突...${NC}"
CONFLICTS_FOUND=false

for file in "${CONFIG_FILES[@]}"; do
    if git diff HEAD origin/main --name-only | grep -q "^$file$"; then
        echo -e "  ${RED}⚠️  配置文件 $file 在远程有更新${NC}"
        CONFLICTS_FOUND=true
    fi
done

# 4. 暂存本地修改
echo -e "\n${YELLOW}💾 暂存本地修改...${NC}"
git stash push -m "Auto stash before update $(date)"

# 5. 拉取远程更新
echo -e "\n${GREEN}⬇️  拉取远程更新...${NC}"
git pull origin main

# 6. 恢复配置文件
echo -e "\n${YELLOW}🔧 恢复本地配置文件...${NC}"

if [ "$CONFLICTS_FOUND" = true ]; then
    echo -e "${YELLOW}⚠️  检测到配置文件冲突，需要手动处理${NC}"
    
    # 从备份恢复配置文件
    for file in "${CONFIG_FILES[@]}"; do
        backup_path="$BACKUP_DIR/$file"
        if [ -f "$backup_path" ]; then
            cp "$backup_path" "$file"
            echo -e "  ✅ 已从备份恢复: $file"
        fi
    done
    
    echo -e "\n${BLUE}📋 后续处理建议:${NC}"
    echo -e "1. 检查远程配置文件的新增配置项:"
    for file in "${CONFIG_FILES[@]}"; do
        if git diff HEAD~$COMMITS_BEHIND HEAD --name-only | grep -q "^$file$"; then
            echo -e "   git show HEAD:$file > ${file}.remote"
            echo -e "   diff $file ${file}.remote"
        fi
    done
    echo -e "2. 手动合并需要的新配置项到本地配置文件"
    echo -e "3. 删除临时文件: rm *.remote"
    
else
    # 没有冲突，直接恢复暂存的修改
    echo -e "${GREEN}✅ 没有配置文件冲突，恢复本地修改...${NC}"
    if git stash list | grep -q "Auto stash before update"; then
        git stash pop
    fi
fi

# 7. 显示更新结果
echo -e "\n${GREEN}🎉 项目更新完成!${NC}"
echo -e "\n${BLUE}📊 更新摘要:${NC}"
echo -e "  • 拉取了 $COMMITS_BEHIND 个新提交"
echo -e "  • 配置文件备份位置: $BACKUP_DIR"
echo -e "  • 本地配置文件已保留"

# 8. 显示当前状态
echo -e "\n${BLUE}📋 当前Git状态:${NC}"
git status --short

echo -e "\n${YELLOW}💡 提示:${NC}"
echo -e "  • 备份文件保存在: $BACKUP_DIR"
echo -e "  • 如需回滚，可以从备份恢复配置文件"
echo -e "  • 建议检查配置文件是否需要添加新的配置项"

# 9. 检查服务是否需要重启
echo -e "\n${BLUE}🔄 服务重启提醒:${NC}"
echo -e "  如果配置文件有重要修改，建议重启相关服务:"
echo -e "  • 后端服务: cd genie-backend && ./start.sh"
echo -e "  • 工具服务: cd genie-tool && ./start.sh"
echo -e "  • 客户端服务: cd genie-client && ./start.sh"