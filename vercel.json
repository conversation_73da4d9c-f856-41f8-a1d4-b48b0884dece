# 优化的Vercel配置
{
  "buildCommand": "cd ui && npm install && npm run build",
  "outputDirectory": "ui/dist",
  "installCommand": "cd ui && npm install",
  "devCommand": "cd ui && npm run dev",
  "framework": "vite",
  "functions": {
    "ui/dist/**": {
      "maxDuration": 10
    }
  },
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/web/(.*)",
      "destination": "https://your-backend-url.railway.app/web/$1"
    },
    {
      "source": "/api/(.*)",
      "destination": "https://your-backend-url.railway.app/api/$1"
    },
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "env": {
    "VITE_API_BASE_URL": "https://your-backend-url.railway.app",
    "VITE_TOOL_SERVICE_URL": "https://your-tool-service.railway.app",
    "VITE_MCP_SERVICE_URL": "https://your-mcp-service.railway.app"
  }
}